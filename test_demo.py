#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
chat4data_engine 服务测试demo
测试HTML页面元素分析服务的各个接口
"""

import requests
import json
import time

# 服务地址
BASE_URL = "http://localhost:18603"

# 测试用的HTML内容
TEST_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>测试页面</title>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">商品列表页面</h1>
            <nav class="navigation">
                <a href="#" class="nav-link">首页</a>
                <a href="#" class="nav-link">商品</a>
                <a href="#" class="nav-link">关于我们</a>
            </nav>
        </header>
        
        <main class="main-content">
            <div class="search-section">
                <input type="text" class="search-input" placeholder="搜索商品...">
                <button class="search-btn">搜索</button>
            </div>
            
            <div class="product-list">
                <div class="product-item" data-id="1">
                    <img src="product1.jpg" class="product-image" alt="商品1">
                    <div class="product-info">
                        <h3 class="product-title">苹果手机 iPhone 15</h3>
                        <p class="product-price">¥5999</p>
                        <p class="product-description">最新款苹果手机，性能强劲</p>
                        <button class="buy-btn">立即购买</button>
                    </div>
                </div>
                
                <div class="product-item" data-id="2">
                    <img src="product2.jpg" class="product-image" alt="商品2">
                    <div class="product-info">
                        <h3 class="product-title">华为 Mate 60</h3>
                        <p class="product-price">¥6999</p>
                        <p class="product-description">华为旗舰手机，拍照出色</p>
                        <button class="buy-btn">立即购买</button>
                    </div>
                </div>
            </div>
            
            <div class="pagination">
                <a href="#" class="page-link">上一页</a>
                <a href="#" class="page-link active">1</a>
                <a href="#" class="page-link">2</a>
                <a href="#" class="page-link">3</a>
                <a href="#" class="page-link">下一页</a>
            </div>
        </main>
        
        <footer class="footer">
            <p>&copy; 2024 测试商城. 保留所有权利.</p>
        </footer>
    </div>
</body>
</html>
"""

def test_api_endpoint(endpoint, data, description):
    """测试API端点的通用函数"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"接口: {endpoint}")
    print(f"{'='*60}")
    
    try:
        response = requests.post(f"{BASE_URL}{endpoint}", json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print(f"响应状态码: {response.status_code}")
            print("响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"❌ 请求失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_analyze_html():
    """测试HTML结构分析"""
    data = {
        "data": TEST_HTML,
        "mode": "xpath",
        "language": "zh_CN"
    }
    test_api_endpoint("/analyze_html", data, "HTML页面主要结构分析")

def test_extract_fields():
    """测试字段提取"""
    data = {
        "data": TEST_HTML,
        "fields": ["商品标题", "商品价格", "购买按钮"],
        "mode": "xpath",
        "language": "zh_CN"
    }
    test_api_endpoint("/extract_fields", data, "提取指定字段信息")

def test_extract_fields_by_group():
    """测试分组字段提取"""
    data = {
        "data": TEST_HTML,
        "fields": ["商品标题", "商品价格", "购买按钮"],
        "mode": "xpath",
        "language": "zh_CN"
    }
    test_api_endpoint("/extract_fields_by_group", data, "分组提取字段信息")

def test_extract_pagination_info():
    """测试分页信息提取"""
    # 提取分页部分的HTML
    pagination_html = """
    <div class="pagination">
        <a href="#" class="page-link">上一页</a>
        <a href="#" class="page-link active">1</a>
        <a href="#" class="page-link">2</a>
        <a href="#" class="page-link">3</a>
        <a href="#" class="page-link">下一页</a>
    </div>
    """
    
    data = {
        "data": pagination_html,
        "mode": "xpath",
        "language": "zh_CN"
    }
    test_api_endpoint("/extract_pagination_info", data, "分页器信息分析")

def test_reduce_html():
    """测试HTML精简"""
    data = {
        "data": TEST_HTML,
        "mode": "xpath",
        "language": "zh_CN"
    }
    test_api_endpoint("/reduce_html", data, "精简页面元素")

def test_find_similar_group():
    """测试查找相似元素组"""
    data = {
        "data": TEST_HTML,
        "mode": "xpath",
        "language": "zh_CN"
    }
    test_api_endpoint("/find_similar_group", data, "查找相似元素组")

def test_get_token_counts():
    """测试token数量计算"""
    data = {
        "data": TEST_HTML
    }
    test_api_endpoint("/get_token_counts", data, "计算文本token数量")

def test_chat_messages():
    """测试AI对话服务"""
    data = {
        "messages": [
            {
                "role": "user",
                "content": "请帮我分析一下这个HTML页面的结构，主要有哪些部分？"
            }
        ],
        "stream": False
    }
    test_api_endpoint("/chat-messages", data, "AI对话服务测试")

def main():
    """主测试函数"""
    print("🚀 开始测试 chat4data_engine 服务")
    print(f"服务地址: {BASE_URL}")
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ 服务运行正常")
        else:
            print("❌ 服务响应异常")
            return
    except:
        print("❌ 无法连接到服务，请确保服务已启动")
        return
    
    # 执行各项测试
    test_analyze_html()
    time.sleep(1)  # 避免请求过快
    
    test_extract_fields()
    time.sleep(1)
    
    test_extract_fields_by_group()
    time.sleep(1)
    
    test_extract_pagination_info()
    time.sleep(1)
    
    test_reduce_html()
    time.sleep(1)
    
    test_find_similar_group()
    time.sleep(1)
    
    test_get_token_counts()
    time.sleep(1)
    
    test_chat_messages()
    
    print(f"\n{'='*60}")
    print("🎉 所有测试完成!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
