
# LLM网页元素分析

## 项目结构

## 服务部分
主要功能
1. HTML页面元素分析
    - /analyze_html: 分析网页主要结构
    - /extract_fields: 提取页面字段信息
    - /extract_pagination_info: 分析分页器信息
    - /reduce_html: 精简页面元素
    - /get_token_counts: 辅助计算文本token数量

支持两种定位模式：
    - XPath模式
    - CSS选择器模式

接口文档：[HTML页面元素分析](https://skieer.feishu.cn/docx/WZzTd9ulmohXw7xWxWsce5manxf?from=from_copylink)

2. AI对话服务
/chat-messages: 提供采集任务相关信息收集的对话功能
    - 支持流式响应(streaming)和阻塞响应(blocking)
    - 支持对话历史记录
    - 支持自定义系统提示词

3. 服务使用和启动：`python run_api.py` 启动网页分析和对话服务