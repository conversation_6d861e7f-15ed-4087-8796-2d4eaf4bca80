# Chat4Data - 智能网页元素分析系统

## 📖 项目简介

Chat4Data 是一个基于大语言模型(LLM)的智能网页元素分析系统，专门用于自动化网页结构分析、数据字段提取和智能对话服务。该系统可以帮助用户快速理解网页结构，提取关键数据字段，并提供智能化的数据采集指导。

## 🎯 核心功能

### 1. 网页结构分析
- **HTML页面主要结构分析** (`/analyze_html`)
  - 自动识别页面中的独立语义区域（导航栏、搜索框、侧边栏、页脚等）
  - 支持XPath和CSS选择器两种定位模式
  - 智能提取各区域的定位路径

### 2. 数据字段提取
- **字段信息提取** (`/extract_fields`)
  - 根据指定字段名称，自动提取对应的XPath或CSS选择器
  - 支持批量字段提取
  - 自动验证和修正提取结果

- **分组字段提取** (`/extract_fields_by_group`)
  - 同时提取字段信息和相似元素组
  - 适用于列表页面的数据提取

### 3. 分页器分析
- **分页信息分析** (`/extract_pagination_info`)
  - 自动识别各种类型的分页控件
  - 支持传统分页、加载更多、无限滚动等模式

### 4. 页面优化
- **HTML精简** (`/reduce_html`)
  - 智能移除冗余元素，减少页面复杂度
  - 保留关键结构信息

- **相似元素组查找** (`/find_similar_group`)
  - 识别页面中的重复结构元素
  - 用于列表数据的批量处理

### 5. AI对话服务
- **智能对话** (`/chat_messages`)
  - 提供数据采集任务的智能指导
  - 支持流式和阻塞式响应
  - 多语言支持

## 🏗️ 项目架构

```
chat4data_ori/
├── core/                    # 核心模块
│   ├── llm_v2.py           # LLM接口封装
│   ├── task_prompt.py      # 任务提示词模板
│   ├── chat_prompt.py      # 对话提示词(中文)
│   ├── chat_prompt_en.py   # 对话提示词(英文)
│   └── utils/              # 工具模块
│       ├── get_elements.py # 元素定位验证
│       ├── token_calculator.py # Token计算
│       └── cleanup_html.py # HTML清理
├── run_api.py              # 主API服务
├── run_chat.py             # 对话服务路由
├── test_demo.py            # 测试示例
├── pyproject.toml          # 项目依赖配置
└── README.md               # 项目说明
```

## 🚀 快速开始

### 环境要求
- Python 3.10+
- Poetry (推荐) 或 pip

### 安装步骤

1. **克隆项目**
```bash
git clone <项目地址>
cd chat4data_ori
```

2. **安装依赖**
```bash
# 使用Poetry (推荐)
poetry install

# 或使用pip
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 设置LLM API配置
export API_BASE="https://litellm.nlp.yuntingai.com"
export API_KEY="your-api-key"
export CHAT_MODEL="gemini-2.0-flash-001"
```

4. **启动服务**
```bash
python run_api.py
```

服务将在 `http://localhost:18603` 启动

### 验证安装
访问 `http://localhost:18603/docs` 查看API文档界面

## 📝 使用示例

### 1. 分析网页结构

```python
import requests

# 准备HTML内容
html_content = """
<html>
<body>
    <nav class="navigation">...</nav>
    <main class="content">...</main>
    <footer class="footer">...</footer>
</body>
</html>
"""

# 调用API
response = requests.post("http://localhost:18603/analyze_html", json={
    "data": html_content,
    "mode": "xpath",
    "language": "zh_CN"
})

result = response.json()
print(result)
```

### 2. 提取指定字段

```python
response = requests.post("http://localhost:18603/extract_fields", json={
    "data": html_content,
    "fields": ["商品标题", "商品价格", "购买按钮"],
    "mode": "xpath",
    "language": "zh_CN"
})
```

### 3. AI对话服务

```python
response = requests.post("http://localhost:18603/chat_messages", json={
    "query": "请帮我分析这个页面的数据结构",
    "inputs": {"current_site": "https://example.com"},
    "response_mode": "blocking"
})
```

## 🔧 API接口详解

### 通用参数说明

| 参数 | 类型 | 说明 | 可选值 |
|------|------|------|--------|
| `data` | string | HTML内容 | - |
| `mode` | string | 定位模式 | `xpath`, `selector` |
| `language` | string | 语言设置 | `zh_CN`, `en_US`, `ja_JP`等 |
| `fields` | array | 目标字段列表 | - |

### 响应格式

```json
{
    "code": 200,
    "message": "Request successful",
    "data": [...],
    "metadata": {
        "usage": {
            "prompt_tokens": 1000,
            "completion_tokens": 500,
            "total_tokens": 1500
        }
    }
}
```

## 🧪 测试

项目提供了完整的测试示例：

```bash
python test_demo.py
```

测试将验证所有API接口的功能。

## 🛠️ 技术栈

- **Web框架**: FastAPI
- **LLM集成**: 支持多种大语言模型 (GPT-4, Claude, Gemini等)
- **HTML解析**: BeautifulSoup4, lxml
- **异步处理**: asyncio
- **依赖管理**: Poetry
- **日志系统**: loguru
- **数据验证**: Pydantic

## 🌟 核心特性

### 智能化分析
- 基于大语言模型的智能网页结构理解
- 自动识别语义区域和数据字段
- 支持复杂页面结构的分析

### 多模式支持
- XPath和CSS选择器双模式
- 多语言界面支持
- 流式和阻塞式响应

### 自动修正
- 智能验证提取结果的准确性
- 自动修正无效的定位路径
- 提高数据提取的成功率

### 高可用性
- 完整的错误处理机制
- 请求重试和容错处理
- 详细的日志记录

## 📊 应用场景

1. **数据采集**: 自动化网页数据提取
2. **页面分析**: 网站结构分析和优化
3. **测试自动化**: Web自动化测试的元素定位
4. **内容管理**: 网页内容的智能分类和提取

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## � 深入理解核心模块

### LLM集成模块 (core/llm_v2.py)

这是系统的核心AI引擎，负责与大语言模型的交互：

**主要功能：**
- 统一的LLM API接口封装
- 支持多种模型切换 (GPT-4, Claude, Gemini等)
- 自动重试机制和错误处理
- 流式和非流式响应支持

**关键配置：**
```python
API_BASE = "https://litellm.nlp.yuntingai.com"  # LLM服务地址
API_KEY = "your-api-key"                        # API密钥
CHAT_MODEL = "gemini-2.0-flash-001"            # 默认模型
```

### 提示词系统 (core/task_prompt.py)

包含了所有任务的专业提示词模板：

**HTML结构分析提示词：**
- 专门训练用于识别网页语义区域
- 支持导航栏、搜索框、侧边栏、页脚等区域识别
- 智能分页控件识别（传统分页、加载更多、无限滚动）

**字段提取提示词：**
- 根据字段名称智能匹配HTML元素
- 支持XPath和CSS选择器生成
- 自动验证和修正机制

### 工具模块 (core/utils/)

**元素定位验证 (get_elements.py)：**
```python
def is_valid_xpath(xpath: str, html_doc: str) -> bool:
    """验证XPath是否有效"""

def is_valid_selector(selector: str, html_doc: str) -> bool:
    """验证CSS选择器是否有效"""
```

**Token计算 (token_calculator.py)：**
```python
def truncate_text_tokens(text: str, max_tokens: int) -> str:
    """按token数量截断文本"""

def truncate_html_by_xpath(html: str, xpaths: List[str]) -> str:
    """根据XPath移除HTML元素"""
```

## 🎨 定制化配置

### 1. 模型配置

支持切换不同的LLM模型：

```bash
# 使用GPT-4
export CHAT_MODEL="gpt-4o-2024-08-06"

# 使用Claude
export CHAT_MODEL="claude-3-5-sonnet-latest"

# 使用Gemini
export CHAT_MODEL="gemini-2.0-flash-001"
```

### 2. 语言配置

系统支持多语言界面：

```python
# 支持的语言代码
SUPPORTED_LANGUAGES = [
    "zh_CN",  # 简体中文
    "en_US",  # 英语
    "ja_JP",  # 日语
    "es_ES",  # 西班牙语
    "de_DE",  # 德语
    "fr_FR",  # 法语
    "it_IT",  # 意大利语
    "ko_KR"   # 韩语
]
```

### 3. 服务配置

可以通过环境变量自定义服务配置：

```bash
export HOST="0.0.0.0"      # 服务监听地址
export PORT="18603"        # 服务端口
export WORKERS="2"         # 工作进程数
export VERSION="EN"        # 界面语言版本
```

## 🚨 常见问题与解决方案

### Q1: 服务启动失败
**问题：** `ModuleNotFoundError: No module named 'core'`
**解决：** 确保在项目根目录下运行，并正确安装了依赖

### Q2: API请求超时
**问题：** 请求LLM服务时超时
**解决：** 检查网络连接和API_KEY配置，可以增加超时时间

### Q3: XPath提取失败
**问题：** 生成的XPath无法定位到元素
**解决：** 启用自动修正功能 `enable_refine=True`

### Q4: 内存占用过高
**问题：** 处理大型HTML文件时内存不足
**解决：** 使用 `/reduce_html` 接口先精简HTML内容

## 📈 性能优化建议

### 1. HTML预处理
```python
# 在分析前先精简HTML
response = requests.post("/reduce_html", json={
    "data": large_html_content
})
simplified_html = response.json()["data"]["truncate_html"]
```

### 2. 批量处理
```python
# 一次性提取多个字段，而不是逐个请求
fields = ["标题", "价格", "描述", "图片", "链接"]
response = requests.post("/extract_fields", json={
    "data": html_content,
    "fields": fields
})
```

### 3. 缓存机制
对于相同的HTML内容，可以缓存分析结果避免重复计算。

## 🔐 安全考虑

### 1. API密钥保护
```bash
# 使用环境变量而不是硬编码
export API_KEY="your-secret-key"
```

### 2. 输入验证
系统自动验证输入数据：
- HTML内容不能为空
- 字段列表不能为空
- 模式参数必须是有效值

### 3. 错误处理
完整的异常捕获和错误信息返回，避免敏感信息泄露。

## 🧩 扩展开发

### 添加新的分析功能

1. **创建提示词模板**
```python
# 在 core/task_prompt.py 中添加
new_analysis_prompt = Prompt(
    system="你的系统提示词...",
    user="用户输入模板..."
)
```

2. **添加API端点**
```python
# 在 run_api.py 中添加
@app.post("/new_analysis")
async def new_analysis(request: DataRequest):
    # 实现分析逻辑
    pass
```

3. **添加测试用例**
```python
# 在 test_demo.py 中添加
def test_new_analysis():
    # 测试新功能
    pass
```

## 📚 学习资源

### 相关技术文档
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [XPath语法指南](https://www.w3.org/TR/xpath/)
- [CSS选择器参考](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Selectors)
- [BeautifulSoup文档](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)

### 最佳实践
1. 优先使用稳定的class属性进行元素定位
2. 避免使用位置索引，使用语义化的选择器
3. 对于动态内容，使用contains()函数进行模糊匹配
4. 定期验证和更新提取规则

## �📄 许可证

本项目采用 MIT 许可证。
