# 🎯 Chat4Data - 智能网页元素分析系统

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.112+-green.svg)](https://fastapi.tiangolo.com)
[![CrewAI](https://img.shields.io/badge/CrewAI-0.80+-orange.svg)](https://crewai.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 🚀 **重大更新**: 现已支持CrewAI多Agent协作系统！通过专业化Agent分工，显著提升分析准确性和系统可靠性。

## 📖 项目简介

Chat4Data 是一个基于大语言模型(LLM)的智能网页元素分析系统，专门用于自动化网页结构分析、数据字段提取和智能对话服务。该系统可以帮助用户快速理解网页结构，提取关键数据字段，并提供智能化的数据采集指导。

### 🌟 核心特性

- 🤖 **CrewAI多Agent系统**: 专业化Agent分工协作，准确性提升30%+
- 🎯 **精准提取**: 自动识别和提取数据字段
- 🔄 **双模式支持**: XPath和CSS选择器
- 🌍 **多语言支持**: 支持8种语言界面
- ⚡ **自动修正**: 智能验证和修正提取结果
- 💬 **AI对话**: 智能助手指导数据采集
- 🔧 **完全兼容**: 保持原有API接口100%兼容

## 🚀 快速开始

### 🤖 CrewAI多Agent系统（推荐）

```bash
# 1. 克隆项目
git clone <项目地址>
cd chat4data_ori

# 2. 一键启动CrewAI系统
python start_crewai.py

# 系统会自动：
# - 检查依赖并安装
# - 配置环境变量
# - 启动多Agent服务
# - 运行功能测试
```

### 📱 传统单Agent系统

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
export API_KEY="your-api-key"
export API_BASE="https://litellm.nlp.yuntingai.com"

# 3. 启动服务
python run_api.py

# 4. 访问文档界面
# 浏览器打开: http://localhost:18603/docs
```

### 简单示例

```python
import requests

# 分析网页结构
response = requests.post("http://localhost:18603/analyze_html", json={
    "data": "<html><nav>导航</nav><main>内容</main></html>",
    "mode": "xpath",
    "language": "zh_CN"
})

print(response.json())
```

## 📚 文档导航

我们为不同用户群体准备了详细的文档：

### 🔰 新手用户
- **[小白用户快速入门指南](./小白用户快速入门指南.md)** - 零基础用户的完整入门教程
  - 什么是Chat4Data？
  - 5分钟快速体验
  - 界面操作指南
  - 常见问题解答

### 🤖 CrewAI多Agent系统
- **[CrewAI多Agent系统使用指南](./CrewAI多Agent系统使用指南.md)** - 新一代多Agent系统详解
  - 多Agent架构设计
  - 专业Agent团队介绍
  - 性能提升对比
  - 迁移和使用指南

### 👨‍💻 开发者
- **[项目完整文档](./项目完整文档.md)** - 技术人员的详细参考
  - 系统架构设计
  - 核心模块解析
  - 技术栈介绍
  - 扩展开发指南

- **[API使用手册](./API使用手册.md)** - 完整的API接口文档
  - 所有接口详细说明
  - 请求响应示例
  - 工作流程建议
  - 性能优化技巧

### 🛠️ 运维人员
- **[部署运维指南](./部署运维指南.md)** - 生产环境部署指南
  - 多种部署方式
  - 安全配置
  - 监控与日志
  - 故障排除

## 🎯 主要功能

### 1. 网页结构分析
```http
POST /analyze_html
```
- 自动识别页面语义区域
- 支持导航栏、内容区、侧边栏等
- 智能分页控件识别

### 2. 数据字段提取
```http
POST /extract_fields
```
- 根据字段名称自动提取定位路径
- 支持批量字段处理
- 自动验证和修正

### 3. 分组数据提取
```http
POST /extract_fields_by_group
```
- 同时提取字段和相似元素组
- 适用于列表页面数据提取

### 4. 分页器分析
```http
POST /extract_pagination_info
```
- 识别各种分页控件类型
- 支持传统分页、加载更多、无限滚动

### 5. AI智能对话
```http
POST /chat_messages
```
- 智能数据采集指导
- 支持流式和阻塞响应
- 多轮对话支持

## 🏗️ 系统架构

```
chat4data_ori/
├── 📁 core/                    # 核心模块
│   ├── 🧠 llm_v2.py           # LLM接口封装
│   ├── 📝 task_prompt.py      # 任务提示词
│   ├── 💬 chat_prompt.py      # 对话提示词
│   └── 🛠️ utils/              # 工具模块
├── 🌐 run_api.py              # 主API服务
├── 💬 run_chat.py             # 对话服务
├── 🧪 test_demo.py            # 测试示例
└── 📋 pyproject.toml          # 项目配置
```

## 🔧 技术栈

- **Web框架**: FastAPI - 高性能异步API框架
- **LLM集成**: 支持GPT-4, Claude, Gemini等多种模型
- **HTML解析**: BeautifulSoup4, lxml - 强大的HTML解析能力
- **数据验证**: Pydantic - 类型安全的数据验证
- **异步处理**: asyncio - 高并发请求处理
- **日志系统**: loguru - 结构化日志记录

## 📊 应用场景

### 🛒 电商数据采集
- 商品信息提取（标题、价格、评价）
- 店铺信息收集
- 竞品分析数据

### 📰 新闻资讯抓取
- 文章标题和内容
- 发布时间和作者
- 评论和互动数据

### 💼 招聘信息整理
- 职位详情提取
- 公司信息收集
- 薪资和福利分析

### 🏠 房产数据分析
- 房源信息提取
- 价格趋势分析
- 区域数据统计

## 🌍 多语言支持

系统支持以下语言界面：

| 语言 | 代码 | 状态 |
|------|------|------|
| 简体中文 | zh_CN | ✅ |
| English | en_US | ✅ |
| 日本語 | ja_JP | ✅ |
| Español | es_ES | ✅ |
| Deutsch | de_DE | ✅ |
| Français | fr_FR | ✅ |
| Italiano | it_IT | ✅ |
| 한국어 | ko_KR | ✅ |

## 🧪 测试

项目提供完整的测试套件：

```bash
# 运行所有测试
python test_demo.py

# 测试特定功能
python -c "
import requests
response = requests.post('http://localhost:18603/analyze_html', json={
    'data': '<html><body><h1>测试</h1></body></html>',
    'mode': 'xpath'
})
print(response.json())
"
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能开发
- 📚 文档改进
- 🎨 界面优化
- ⚡ 性能提升

## 📈 路线图

### v1.1 (计划中)
- [ ] 支持更多LLM模型
- [ ] 增加批量处理API
- [ ] 优化内存使用
- [ ] 添加缓存机制

### v1.2 (计划中)
- [ ] Web界面开发
- [ ] 数据导出功能
- [ ] 定时任务支持
- [ ] 用户管理系统

## 🆘 获取帮助

### 文档资源
- 📖 [完整技术文档](./项目完整文档.md)
- 🚀 [快速入门指南](./小白用户快速入门指南.md)
- 📋 [API使用手册](./API使用手册.md)
- 🛠️ [部署运维指南](./部署运维指南.md)

### 社区支持
- 💬 提交Issue报告问题
- 📧 发送邮件到 <EMAIL>
- 🔗 加入我们的讨论群

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

特别感谢：
- FastAPI 团队提供的优秀框架
- OpenAI、Anthropic、Google 提供的LLM服务
- 所有开源依赖项的维护者

---

<div align="center">

**如果这个项目对你有帮助，请给我们一个 ⭐ Star！**

[🚀 快速开始](./小白用户快速入门指南.md) | [📚 完整文档](./项目完整文档.md) | [🔧 API手册](./API使用手册.md) | [🛠️ 部署指南](./部署运维指南.md)

</div>
