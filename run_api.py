import re
import time
from typing import List, Literal

import tiktoken
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, field_validator

from core.llm_v2 import message_formatter, request_llm
from core.task_prompt import (
    dom_reducer_prompt_xpath,
    field_extract_prompt_selector,
    field_extract_prompt_xpath,
    field_first_prompt_xpath,
    html_structure_prompt_selector,
    html_structure_prompt_xpath,
    pagination_prompt_selector,
    pagination_prompt_xpath,
    reflection_prompt_selector,
    reflection_prompt_xpath,
    reflection_prompt_xpath_area,
    specified_field_extract_prompt_selector,
    specified_field_extract_prompt_xpath,
    specified_field_first_extract_prompt_xpath,
    specified_html_structure_prompt_selector,
    specified_html_structure_prompt_xpath,
)
from core.utils.get_elements import is_valid_selector, is_valid_xpath
from core.utils.token_calculator import truncate_html_by_xpath, truncate_text_tokens
from run_chat import router as chat_router

app = FastAPI()
app.include_router(chat_router)
# 允许跨域的源
origins = [
    "*"
    # "https://your-frontend-domain.com",
]

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # 允许的域名列表
    allow_credentials=True,
    allow_methods=["*"],  # 允许的 HTTP 方法，如 GET、POST 等
    allow_headers=["*"],  # 允许的 HTTP 头部字段
)

METADATA = {
    "trace_user_id": "",
    "trace_name": "chat4data:analysis",
    "generation_name": "html_analysis",
    "tags": ["analysis"],
}


# 定义请求模型
class DataRequest(BaseModel):
    data: str
    fields: List[str] = []
    mode: Literal["xpath", "selector"] = "xpath"
    language: Literal[
        "zh_CN", "en_US", "ja_JP", "es_ES", "de_DE", "fr_FR", "it_IT", "ko_KR"
    ] = "zh_CN"

    # 校验data字段
    @field_validator("data")
    def check_not_empty(cls, v):
        if not v.strip():  # 检查字符串是否为空或仅包含空白字符
            raise ValueError("The 'data' field cannot be empty or just whitespace.")
        return v


def replace_contains_class_condition(xpath: str) -> str:
    """替换xpath中的contains(@class, "value")为contains(concat(' ', normalize-space(@class), ' '), ' value ')"""

    # 使用正则表达式查找并替换
    pattern = r"contains\(@class,\s*\'([^\']+)\'\)"  # 修改为匹配单引号
    replacement = r"contains(concat(' ', normalize-space(@class), ' '), ' \1 ')"
    modified_xpath = re.sub(pattern, replacement, xpath)
    return modified_xpath


def validate_and_refine_fields(
    result: List[dict],
    html_doc: str,
    enable_refine: bool = True,
    mode: str = "xpath",
    language: str = "zh",
) -> List[dict]:
    """验证xpath/selector并在需要时进行修正"""
    error_dict = {}
    # 逐个验证xpath/selector
    for item in result:
        field = item.get("type") or item.get("field")  # 兼容两种字段名
        path = item.get("xpath") if mode == "xpath" else item.get("selector")
        if path:
            if mode == "xpath":
                # path = replace_contains_class_condition(
                #     path
                # )  # 替换xpath中的contains(@class)
                if is_valid_xpath(path, html_doc):
                    item["xpath"] = path  # 更新替换后的xpath
                else:
                    error_dict[field] = path
            else:
                if is_valid_selector(path, html_doc):
                    continue
                else:
                    error_dict[field] = path
    print(f"{error_dict=}")
    # 是否开启修正
    if enable_refine and error_dict:
        correct_dict = refine_fields(html_doc, error_dict, mode, language)
        for item in result:
            field = item.get("type") or item.get("field")
            if field in correct_dict:
                if mode == "xpath":
                    # path = replace_contains_class_condition(correct_dict[field])
                    item["xpath"] = correct_dict[field]
                else:
                    item["selector"] = correct_dict[field]
    return result


def validate_and_refine_area(
    result: List[dict],
    html_doc: str,
    enable_refine: bool = True,
    mode: str = "xpath",
    language: str = "zh",
) -> List[dict]:
    """验证xpath/selector并在需要时进行修正"""
    error_dict = {}
    # 逐个验证xpath/selector
    for item in result:
        xpath = item.get("xpath") if mode == "xpath" else item.get("container_selector")
        children_path = (
            item.get("children_xpath")
            if mode == "xpath"
            else item.get("children_selector")
        )
        name = item.get("name")

        if mode == "xpath":
            if is_valid_xpath(xpath, html_doc) and is_valid_xpath(
                children_path, html_doc
            ):
                pass
                # item["xpath"] = replace_contains_class_condition(xpath)
                # item["children_xpath"] = replace_contains_class_condition(children_path)
            else:
                error_dict[name] = {
                    "description": item.get("description"),
                    "xpath": xpath,
                    "children_xpath": children_path,
                }

    print(f"{error_dict=}")
    # 是否开启修正
    if enable_refine and error_dict:
        correct_dict = refine_areas(html_doc, error_dict, mode, language)
        for item in result:
            name = item.get("name")
            if name in correct_dict:
                if mode == "xpath":
                    # item["xpath"] = replace_contains_class_condition(path)
                    item["xpath"] = correct_dict[name]["xpath"]
                    item["children_xpath"] = correct_dict[name]["children_xpath"]
    return result


@app.post("/analyze_html", description="分析网页主要结构")
async def process_webpage(request: DataRequest, enable_refine: bool = True):
    try:
        truncated_data = await truncate_html(request)
        html_doc = truncated_data["data"].get("truncate_html")
        system_kwargs = {"language": request.language}
        user_kwargs = {"html_doc": html_doc}
        # 是否提取给定字段
        if request.fields:
            user_kwargs["fields"] = request.fields
            prompt = (
                specified_html_structure_prompt_xpath
                if request.mode == "xpath"
                else specified_html_structure_prompt_selector
            )
        else:
            prompt = (
                html_structure_prompt_xpath
                if request.mode == "xpath"
                else html_structure_prompt_selector
            )

        messages = message_formatter(prompt, system_kwargs, user_kwargs)
        # gpt-4o-2024-08-06, chatgpt-4o-latest, claude-3-5-haiku-latest
        METADATA["trace_id"] = f"chat4data:analysis:{time.strftime('%Y%m%d%H')}"
        llm_result = request_llm(
            messages,
            model="gemini-2.0-flash-001",
            verbose=True,
            parse_json=True,
            metadata=METADATA,
        )
        # 验证和修正
        result = validate_and_refine_fields(
            llm_result["content"],
            html_doc,
            enable_refine,
            request.mode,
            request.language,
        )
        metadata = {"usage": llm_result["usage"]}
        response = {
            "code": 200,
            "message": "Request successful",
            "data": result,
            "metadata": metadata,
        }
    except Exception as e:
        # 捕获异常并返回错误信息
        import traceback

        traceback.print_exc()
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    return response


# 定义请求模型
class FieldExtraction(BaseModel):
    name: str = ""
    data: str
    fields: List[str] = []
    groups: List[str] = []
    mode: Literal["xpath", "selector"] = "xpath"
    language: Literal[
        "zh_CN", "en_US", "ja_JP", "es_ES", "de_DE", "fr_FR", "it_IT", "ko_KR"
    ] = "zh_CN"

    # 校验data字段
    @field_validator("data")
    def check_not_empty(cls, v):
        if not v.strip():  # 检查字符串是否为空或仅包含空白字符
            raise ValueError("The 'data' field cannot be empty or just whitespace.")
        return v


@app.post("/extract_fields", description="分析给定页面的主要字段")
async def extract_fields(request: FieldExtraction, enable_refine: bool = True):
    try:
        # truncated_html_docs = truncate_text_tokens(
        #     request.data, max_tokens=120000, encoding_name="o200k_base"
        # )
        truncated_data = await truncate_html(request)
        truncated_html_doc = truncated_data["data"].get("truncate_html")
        print(f"{request.name=}, {request.fields=}")

        # 抽取指定分块的名字
        system_kwargs = {"language": request.language}
        user_kwargs = {"name": request.name, "html_doc": truncated_html_doc}

        # 是否提取给定字段
        if request.fields:
            user_kwargs["fields"] = request.fields
            prompt = (
                specified_field_extract_prompt_xpath
                if request.mode == "xpath"
                else specified_field_extract_prompt_selector
            )
        else:
            prompt = (
                field_extract_prompt_xpath
                if request.mode == "xpath"
                else field_extract_prompt_selector
            )

        messages = message_formatter(prompt, system_kwargs, user_kwargs)
        # claude-3-5-sonnet-latest,gpt-4o-mini,claude-3-5-haiku-latest
        METADATA["trace_id"] = f"chat4data:analysis:{time.strftime('%Y%m%d%H')}"
        llm_result = request_llm(
            messages,
            model="gemini-2.0-flash-001",
            verbose=True,
            parse_json=True,
            max_completion_tokens=8192,
            metadata=METADATA,
        )

        # 验证和修正
        result = validate_and_refine_fields(
            llm_result["content"],
            truncated_html_doc,
            enable_refine,
            request.mode,
            request.language,
        )
        # result = llm_result["content"]
        metadata = {"usage": llm_result["usage"]}
        response = {
            "code": 200,
            "message": "Request successful",
            "data": result,
            "metadata": metadata,
        }
    except Exception as e:
        # 捕获异常并返回错误信息
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    return response


# 定义请求模型
class SpcifiedFieldExtraction(BaseModel):
    name: str = ""
    data: str
    fields: List[str]
    mode: Literal["xpath", "selector"] = "xpath"
    language: Literal["zh", "en"] = "zh"

    # 校验data字段
    @field_validator("data", "fields")
    def check_not_empty(cls, v):
        if isinstance(v, str) and not v.strip():  # 检查字符串是否为空或仅包含空白字符
            raise ValueError("The 'data' field cannot be empty or just whitespace.")
        if isinstance(v, list) and not v:  # 检查列表是否为空
            raise ValueError("The 'fields' field cannot be empty.")
        return v


@app.post("/extract_fields_by_group", description="分析给定页面的主要字段和区域")
async def extract_fields_by_group(request: FieldExtraction, enable_refine: bool = True):
    try:
        # truncated_html_docs = truncate_text_tokens(
        #     request.data, max_tokens=120000, encoding_name="o200k_base"
        # )
        truncated_data = await truncate_html(request)
        truncated_html_doc = truncated_data["data"].get("truncate_html")
        print(f"{request.name=}, {request.fields=}")

        # 抽取指定分块的名字
        system_kwargs = {"language": request.language}
        user_kwargs = {"name": request.name, "html_doc": truncated_html_doc}

        # 是否提取给定字段
        if request.fields:
            user_kwargs["fields"] = request.fields
            user_kwargs["groups"] = request.groups
            prompt = (
                specified_field_first_extract_prompt_xpath
                if request.mode == "xpath"
                else specified_field_extract_prompt_selector
            )
        else:
            prompt = (
                # field_extract_prompt_xpath
                field_first_prompt_xpath
                if request.mode == "xpath"
                else field_extract_prompt_selector
            )

        messages = message_formatter(prompt, system_kwargs, user_kwargs)
        # claude-3-5-sonnet-latest,gpt-4o-mini,claude-3-5-haiku-latest
        METADATA["trace_id"] = f"chat4data:analysis:{time.strftime('%Y%m%d%H')}"
        llm_result = request_llm(
            messages,
            model="gemini-2.0-flash-001",
            verbose=True,
            parse_json=True,
            max_completion_tokens=8192,
            metadata=METADATA,
        )

        # 验证和修正
        result = llm_result["content"]
        result["fields"] = validate_and_refine_fields(
            result["fields"],
            truncated_html_doc,
            enable_refine,
            request.mode,
            request.language,
        )
        result["similar_groups"] = validate_and_refine_area(
            result["similar_groups"],
            truncated_html_doc,
            enable_refine=True,
            mode=request.mode,
            language=request.language,
        )
        metadata = {"usage": llm_result["usage"]}
        response = {
            "code": 200,
            "message": "Request successful",
            "data": result,
            "metadata": metadata,
        }
    except Exception as e:
        # 捕获异常并返回错误信息
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    return response


@app.post("/extract_pagination_info", description="分析给定分页器html的xpath字段")
async def extract_specified_fields(request: DataRequest, enable_refine: bool = True):
    try:
        system_kwargs = {"language": request.language}
        user_kwargs = {"html_doc": request.data}
        messages = message_formatter(
            pagination_prompt_xpath
            if request.mode == "xpath"
            else pagination_prompt_selector,
            system_kwargs,
            user_kwargs,
        )
        # claude-3-5-sonnet-latest, gpt-4o-mini
        METADATA["trace_id"] = f"chat4data:analysis:{time.strftime('%Y%m%d%H')}"
        llm_result = request_llm(
            messages,
            model="gemini-2.0-flash-001",
            verbose=True,
            parse_json=True,
            metadata=METADATA,
        )

        # 验证和修正
        result = validate_and_refine_fields(
            llm_result["content"],
            request.data,
            enable_refine,
            request.mode,
            request.language,
        )
        metadata = {"usage": llm_result["usage"]}
        response = {
            "code": 200,
            "message": "Request successful",
            "data": result,
            "metadata": metadata,
        }
    except Exception as e:
        # 捕获异常并返回错误信息
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    return response


@app.post("/reduce_html", description="精简页面元素")
async def truncate_html(request: DataRequest):
    try:
        html = request.data
        similar_xpaths = []
        while True:
            truncated_html_docs = truncate_text_tokens(
                html, max_tokens=200000, encoding_name="o200k_base"
            )
            if len(truncated_html_docs) == 1:
                break

            system_kwargs = {"language": request.language}
            user_kwargs = {"html_doc": truncated_html_docs[:1]}
            messages = message_formatter(
                dom_reducer_prompt_xpath, system_kwargs, user_kwargs
            )
            llm_result = request_llm(
                messages,
                model="gemini-2.0-flash-001",
                verbose=True,
                parse_json=True,
                metadata=METADATA,
            )
            html = truncate_html_by_xpath(
                html, [item["xpath"] for item in llm_result["content"]]
            )
            similar_xpaths.extend(llm_result["content"])
        response = {
            "code": 200,
            "message": "Request successful",
            "data": {"truncate_html": html, "similar_group": similar_xpaths},
        }
    except Exception as e:
        # 捕获异常并返回错误信息
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    return response


@app.post("/find_similar_group", description="查找相似元素组")
async def find_similar_group(request: DataRequest):
    try:
        html_doc = request.data
        system_kwargs = {"language": request.language}
        user_kwargs = {"html_doc": html_doc}
        messages = message_formatter(
            dom_reducer_prompt_xpath, system_kwargs, user_kwargs
        )
        llm_result = request_llm(
            messages,
            model="gemini-2.0-flash-001",
            verbose=True,
            parse_json=True,
            metadata=METADATA,
        )
        metadata = {"usage": llm_result["usage"]}
        response = {
            "code": 200,
            "message": "Request successful",
            "data": {"similar_group": llm_result["content"]},
            "metadata": metadata,
        }
    except Exception as e:
        # 捕获异常并返回错误信息
        response = {"code": 400, "message": f"Bad Request due to {e}", "data": []}
    return response


@app.post("/get_token_counts", description="计算token数")
async def count_token(request: DataRequest):
    html_text = request.data
    try:
        encoding = tiktoken.get_encoding("o200k_base")
        tokens = len(encoding.encode(html_text))
        response = {
            "code": 200,
            "message": "Request successful",
            "data": {"char_length": len(html_text), "model_tokens": tokens},
        }
    except Exception as e:
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": {}}
    return response


def refine_fields(
    html_doc: str, error_fields: dict, mode: str = "xpath", language: str = "zh"
) -> dict:
    system_kwargs = {"language": language}
    user_kwargs = {
        "html_doc": html_doc,
        "fields": list(error_fields.keys()),
        "result": error_fields,
    }
    prompt = reflection_prompt_xpath if mode == "xpath" else reflection_prompt_selector
    messages = message_formatter(prompt, system_kwargs, user_kwargs)
    # gpt-4o-2024-08-06, chatgpt-4o-latest, o1-mini, claude-3
    llm_result = request_llm(
        messages,
        model="gemini-2.0-flash-001",
        verbose=True,
        parse_json=True,
        max_completion_tokens=8192,
        metadata=METADATA,
    )
    print(llm_result["content"])

    field_key = "xpath" if mode == "xpath" else "selector"
    return {item["field"]: item[field_key] for item in llm_result["content"]}


def refine_areas(
    html_doc: str, error_fields: dict, mode: str = "xpath", language: str = "zh"
) -> dict:
    system_kwargs = {"language": language}
    user_kwargs = {
        "html_doc": html_doc,
        "result": error_fields,
    }
    prompt = reflection_prompt_xpath_area
    messages = message_formatter(prompt, system_kwargs, user_kwargs)
    # gpt-4o-2024-08-06, chatgpt-4o-latest, o1-mini, claude-3
    llm_result = request_llm(
        messages,
        model="gemini-2.0-flash-001",
        verbose=True,
        parse_json=True,
        max_completion_tokens=8192,
        metadata=METADATA,
    )
    print(llm_result["content"])

    return {item["name"]: item for item in llm_result["content"]}


# 运行应用
if __name__ == "__main__":
    import uvicorn

    uvicorn.run("run_api:app", host="0.0.0.0", port=18603, workers=2)
