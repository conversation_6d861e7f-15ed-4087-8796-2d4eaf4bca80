#!/usr/bin/env python

import os
from glob import glob

from pydantic import BaseModel, <PERSON>
from pymongo import MongoClient
from rich.console import Console

from core.llm import find_xpath, prepare_html_docs
from core.utils.chromium import ChromiumLoader

c = Console()

mongo_url = "*****************************************************************"

list_prompt = ""


class Answer(BaseModel):
    class XPath(BaseModel):
        type: str = Field(
            ...,
            description="Type of content to scrape",
            choices=["产品名称", "产品价格"],
        )
        xpath: str

    answer: list[XPath] = []


def content2xpath(content: str, tree):
    """content to xpath"""
    xpaths = []
    try:
        elements = tree.xpath(f"//text()[.='{content}']/..")
        for elem in elements:
            xpaths.append(tree.getroottree().getpath(elem))
    except Exception:
        print(content)
        input()
    return xpaths


def html_doc_mongo():
    collection = MongoClient(mongo_url)["test_transfer"]["GrainsListUrlTemplate"]
    for item in collection.find(
        {}, {"_doc.HtmlSource.Html": 1, "_doc.CrawlTargets": 1}
    ):
        # 读取一个 html_doc
        html_doc = item["_doc"]["HtmlSource"]["Html"]
        if html_doc is None:
            continue
        yield html_doc


def html_doc_urls():
    urls = ["https://www.amazon.com/s?k=Jeans"]
    os.system("mkdir -p ./html_docs")
    for url in urls:
        loader = ChromiumLoader([url], headless=True)
        document = loader.load()

        if not document or not document[0].page_content.strip():
            raise ValueError(
                "No HTML body content found in the document fetched by ChromiumLoader."
            )
        html_doc = document[0].page_content

        # 保存 html_doc
        with open(f"./html_docs/{url.replace('/', '--')}", "w") as f:
            f.write(html_doc)
        yield html_doc


def html_doc_files():
    html_doc_directory = "./html_docs"
    for html_doc_file in glob(f"{html_doc_directory}/*"):
        with open(html_doc_file, "r") as f:
            html_doc = f.read()
        yield html_doc


def main():
    """entry"""

    source = html_doc_files()

    for html_doc in source:
        # 源码预处理: 清洗, 裁剪
        truncated_html_docs = prepare_html_docs(html_doc, max_tokens=100000)
        # 调用大模型分析并展示结果
        for truncated_html_doc in truncated_html_docs:
            find_xpath(
                truncated_html_doc, fields=["产品名称", "产品价格"], verbose=True
            )


if __name__ == "__main__":
    main()
