# 🚀 Chat4Data 部署与运维指南

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **存储**: 10GB可用空间
- **网络**: 稳定的互联网连接（访问LLM API）

### 软件要求
- **操作系统**: Linux/macOS/Windows
- **Python**: 3.10或更高版本
- **包管理器**: Poetry（推荐）或pip

## 🛠️ 安装部署

### 方式1：使用Poetry（推荐）

```bash
# 1. 克隆项目
git clone <项目仓库地址>
cd chat4data_ori

# 2. 安装Poetry（如果未安装）
curl -sSL https://install.python-poetry.org | python3 -

# 3. 安装依赖
poetry install

# 4. 激活虚拟环境
poetry shell

# 5. 启动服务
python run_api.py
```

### 方式2：使用pip

```bash
# 1. 克隆项目
git clone <项目仓库地址>
cd chat4data_ori

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动服务
python run_api.py
```

### 方式3：使用Docker

```bash
# 1. 构建镜像
docker build -t chat4data .

# 2. 运行容器
docker run -d \
  --name chat4data \
  -p 18603:18603 \
  -e API_KEY="your-api-key" \
  -e API_BASE="https://litellm.nlp.yuntingai.com" \
  chat4data
```

## ⚙️ 环境配置

### 必需环境变量

```bash
# LLM API配置
export API_BASE="https://litellm.nlp.yuntingai.com"
export API_KEY="your-api-key-here"
export CHAT_MODEL="gemini-2.0-flash-001"

# 服务配置
export HOST="0.0.0.0"
export PORT="18603"
export WORKERS="2"

# 界面语言
export VERSION="EN"  # EN或CN
```

### 配置文件方式

创建 `.env` 文件：
```env
API_BASE=https://litellm.nlp.yuntingai.com
API_KEY=your-api-key-here
CHAT_MODEL=gemini-2.0-flash-001
HOST=0.0.0.0
PORT=18603
WORKERS=2
VERSION=EN
```

## 🔧 生产环境部署

### 使用Gunicorn

```bash
# 安装Gunicorn
pip install gunicorn

# 启动服务
gunicorn run_api:app \
  --host 0.0.0.0 \
  --port 18603 \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --access-logfile - \
  --error-logfile -
```

### 使用Supervisor

创建配置文件 `/etc/supervisor/conf.d/chat4data.conf`：

```ini
[program:chat4data]
command=/path/to/venv/bin/gunicorn run_api:app --host 0.0.0.0 --port 18603 --workers 4 --worker-class uvicorn.workers.UvicornWorker
directory=/path/to/chat4data_ori
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/chat4data.log
environment=API_KEY="your-api-key",API_BASE="https://litellm.nlp.yuntingai.com"
```

启动服务：
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start chat4data
```

### 使用Systemd

创建服务文件 `/etc/systemd/system/chat4data.service`：

```ini
[Unit]
Description=Chat4Data API Service
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/chat4data_ori
Environment=API_KEY=your-api-key
Environment=API_BASE=https://litellm.nlp.yuntingai.com
ExecStart=/path/to/venv/bin/python run_api.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable chat4data
sudo systemctl start chat4data
```

## 🌐 反向代理配置

### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:18603;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持长连接和流式响应
        proxy_buffering off;
        proxy_cache off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
}
```

### Apache配置

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    
    ProxyPreserveHost On
    ProxyPass / http://127.0.0.1:18603/
    ProxyPassReverse / http://127.0.0.1:18603/
    
    # 支持WebSocket和流式响应
    ProxyPass /ws/ ws://127.0.0.1:18603/ws/
    ProxyPassReverse /ws/ ws://127.0.0.1:18603/ws/
</VirtualHost>
```

## 📊 监控与日志

### 日志配置

系统使用loguru进行日志管理，可以通过环境变量配置：

```bash
export LOG_LEVEL="INFO"
export LOG_FILE="/var/log/chat4data/app.log"
export LOG_ROTATION="1 day"
export LOG_RETENTION="30 days"
```

### 健康检查

创建健康检查脚本 `health_check.py`：

```python
import requests
import sys

def health_check():
    try:
        response = requests.get("http://localhost:18603/docs", timeout=5)
        if response.status_code == 200:
            print("Service is healthy")
            return 0
        else:
            print(f"Service returned status code: {response.status_code}")
            return 1
    except Exception as e:
        print(f"Health check failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(health_check())
```

### 性能监控

使用Prometheus监控（可选）：

```python
# 在run_api.py中添加
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

@app.middleware("http")
async def add_prometheus_metrics(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(time.time() - start_time)
    return response

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

## 🔒 安全配置

### API密钥管理

```bash
# 使用环境变量而不是硬编码
export API_KEY="$(cat /etc/chat4data/api_key)"

# 设置文件权限
chmod 600 /etc/chat4data/api_key
chown root:root /etc/chat4data/api_key
```

### HTTPS配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://127.0.0.1:18603;
        # ... 其他配置
    }
}
```

### 访问控制

```nginx
# IP白名单
location / {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;
    
    proxy_pass http://127.0.0.1:18603;
}

# 基本认证
location / {
    auth_basic "Restricted Access";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    proxy_pass http://127.0.0.1:18603;
}
```

## 🔧 故障排除

### 常见问题

**1. 服务启动失败**
```bash
# 检查端口占用
netstat -tlnp | grep 18603

# 检查Python环境
python --version
pip list | grep fastapi

# 查看详细错误
python run_api.py --debug
```

**2. API请求超时**
```bash
# 检查LLM API连接
curl -H "Authorization: Bearer $API_KEY" $API_BASE/v1/models

# 增加超时时间
export REQUEST_TIMEOUT=60
```

**3. 内存占用过高**
```bash
# 监控内存使用
top -p $(pgrep -f run_api.py)

# 调整工作进程数
export WORKERS=1
```

### 日志分析

```bash
# 查看实时日志
tail -f /var/log/chat4data.log

# 搜索错误
grep -i error /var/log/chat4data.log

# 分析请求统计
awk '/POST/ {print $7}' /var/log/nginx/access.log | sort | uniq -c
```

## 📈 性能优化

### 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化TCP参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 应用级优化

```python
# 在run_api.py中调整
app = FastAPI(
    title="Chat4Data API",
    docs_url="/docs",
    redoc_url="/redoc",
    # 生产环境可以禁用文档
    # docs_url=None,
    # redoc_url=None,
)

# 调整工作进程数
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "run_api:app", 
        host="0.0.0.0", 
        port=18603, 
        workers=4,  # 根据CPU核心数调整
        access_log=False  # 生产环境可以禁用访问日志
    )
```

## 🔄 备份与恢复

### 数据备份

```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/chat4data/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份配置文件
cp -r /path/to/chat4data_ori $BACKUP_DIR/

# 备份日志
cp /var/log/chat4data.log $BACKUP_DIR/

# 压缩备份
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### 自动备份

```bash
# 添加到crontab
0 2 * * * /path/to/backup.sh
```

这份部署运维指南涵盖了从开发环境到生产环境的完整部署流程，包括安全配置、监控、故障排除等关键内容。
