[tool.poetry]
name = "auto-parser"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
requests = "^2.32.3"
pymongo = "^4.8.0"
bs4 = "^0.0.2"
lxml = "^5.2.2"
loguru = "^0.7.2"
rich = "^13.7.1"
pydantic = "^2.8.2"
free-proxy = "^1.1.1"
minify-html = "^0.15.0"
tiktoken = "^0.7.0"
langchain-community = "^0.2.10"
playwright = "^1.45.1"
undetected-playwright = "^0.3.0"
fastapi = "^0.112.0"
uvicorn = "^0.30.5"
pillow = "^10.4.0"
markdownify = "^0.13.1"
sse-starlette = "^2.1.3"
retrying = "^1.3.4"
crewai = "^0.80.0"
crewai-tools = "^0.12.0"


[[tool.poetry.source]]
name = "skieer-nexus"
url = "https://nexus.ops.skieer.com/repository/pypi-group/simple"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

