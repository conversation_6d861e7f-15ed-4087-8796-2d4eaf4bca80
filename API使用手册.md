# 📚 Chat4Data API 详细使用手册

## 🌐 服务概览

Chat4Data 提供了一套完整的RESTful API，用于智能网页分析和数据提取。所有接口都支持JSON格式的请求和响应。

**服务地址：** `http://localhost:18603`
**API文档：** `http://localhost:18603/docs`

## 🔧 通用参数说明

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `data` | string | ✅ | HTML内容 | `"<html>...</html>"` |
| `mode` | string | ❌ | 定位模式 | `"xpath"` 或 `"selector"` |
| `language` | string | ❌ | 界面语言 | `"zh_CN"`, `"en_US"` 等 |
| `fields` | array | ❌ | 目标字段列表 | `["标题", "价格"]` |

### 响应格式

```json
{
    "code": 200,                    // 状态码
    "message": "Request successful", // 状态信息
    "data": [...],                  // 主要数据
    "metadata": {                   // 元数据
        "usage": {                  // Token使用情况
            "prompt_tokens": 1000,
            "completion_tokens": 500,
            "total_tokens": 1500
        }
    }
}
```

## 📋 API接口详解

### 1. 网页结构分析 `/analyze_html`

**功能：** 分析网页的主要语义区域（导航栏、内容区、侧边栏等）

**请求示例：**
```json
{
    "data": "<html><body><nav>导航</nav><main>内容</main></body></html>",
    "mode": "xpath",
    "language": "zh_CN"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "Request successful",
    "data": [
        {
            "type": "导航栏",
            "xpath": "//nav",
            "description": "页面主导航区域"
        },
        {
            "type": "主内容区",
            "xpath": "//main",
            "description": "页面主要内容区域"
        }
    ]
}
```

**使用场景：**
- 初次分析未知网页结构
- 了解页面的整体布局
- 为后续数据提取做准备

### 2. 字段信息提取 `/extract_fields`

**功能：** 根据指定字段名称，自动提取对应的定位路径

**请求示例：**
```json
{
    "name": "商品列表页",
    "data": "<div class='product'><h2>iPhone</h2><span>¥5999</span></div>",
    "fields": ["商品名称", "价格"],
    "mode": "xpath",
    "language": "zh_CN"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "field": "商品名称",
            "xpath": "//h2",
            "description": "商品的标题信息"
        },
        {
            "field": "价格",
            "xpath": "//span",
            "description": "商品的价格信息"
        }
    ]
}
```

**参数说明：**
- `name`: 页面描述，帮助AI理解页面类型
- `fields`: 要提取的字段名称列表

### 3. 分组字段提取 `/extract_fields_by_group`

**功能：** 同时提取字段信息和相似元素组，适用于列表页面

**请求示例：**
```json
{
    "name": "商品列表",
    "data": "<div class='list'><div class='item'>商品1</div><div class='item'>商品2</div></div>",
    "fields": ["商品名称"],
    "groups": ["商品列表"],
    "mode": "xpath"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "fields": [
            {
                "field": "商品名称",
                "xpath": "//div[@class='item']"
            }
        ],
        "similar_groups": [
            {
                "name": "商品列表",
                "description": "重复的商品项目",
                "xpath": "//div[@class='list']",
                "children_xpath": "//div[@class='item']"
            }
        ]
    }
}
```

### 4. 分页信息分析 `/extract_pagination_info`

**功能：** 识别和分析页面中的分页控件

**请求示例：**
```json
{
    "data": "<div class='pagination'><a href='#'>上一页</a><a href='#'>1</a><a href='#'>2</a><a href='#'>下一页</a></div>",
    "mode": "xpath"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "type": "上一页按钮",
            "xpath": "//a[contains(text(), '上一页')]"
        },
        {
            "type": "下一页按钮", 
            "xpath": "//a[contains(text(), '下一页')]"
        },
        {
            "type": "页码链接",
            "xpath": "//a[contains(@href, '#') and text()='1']"
        }
    ]
}
```

### 5. HTML精简 `/reduce_html`

**功能：** 移除冗余元素，简化HTML结构

**请求示例：**
```json
{
    "data": "<html><head>...</head><body><div>重要内容</div><script>...</script></body></html>",
    "mode": "xpath"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "truncate_html": "<body><div>重要内容</div></body>",
        "similar_group": [
            {
                "xpath": "//script",
                "description": "移除的脚本标签"
            }
        ]
    }
}
```

### 6. 相似元素组查找 `/find_similar_group`

**功能：** 识别页面中的重复结构元素

**请求示例：**
```json
{
    "data": "<ul><li>项目1</li><li>项目2</li><li>项目3</li></ul>",
    "mode": "xpath"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "similar_group": [
            {
                "name": "列表项",
                "xpath": "//li",
                "count": 3,
                "description": "重复的列表项元素"
            }
        ]
    }
}
```

### 7. Token计算 `/get_token_counts`

**功能：** 计算文本的Token数量，用于评估处理成本

**请求示例：**
```json
{
    "data": "这是一段需要计算token数量的文本内容"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "char_length": 20,
        "model_tokens": 15
    }
}
```

### 8. AI对话服务 `/chat_messages`

**功能：** 提供智能对话，帮助用户解决数据提取问题

**请求示例：**
```json
{
    "query": "我想从电商网站提取商品信息，应该怎么做？",
    "inputs": {
        "current_site": "https://example.com",
        "target_fields": "商品名称,价格,评价"
    },
    "response_mode": "blocking",
    "conversation_id": "conv_123"
}
```

**响应示例：**
```json
{
    "message_id": "msg_456",
    "conversation_id": "conv_123",
    "answer": {
        "suggestion": "建议先使用analyze_html分析页面结构",
        "steps": [
            "1. 获取页面HTML源码",
            "2. 调用/analyze_html接口分析结构", 
            "3. 使用/extract_fields提取具体字段"
        ]
    }
}
```

## 🔄 工作流程建议

### 标准分析流程

```mermaid
graph TD
    A[获取HTML] --> B[/analyze_html 分析结构]
    B --> C[/extract_fields 提取字段]
    C --> D[验证结果]
    D --> E{结果准确?}
    E -->|是| F[完成]
    E -->|否| G[/chat_messages 寻求帮助]
    G --> C
```

### 复杂页面处理流程

```mermaid
graph TD
    A[复杂HTML] --> B[/reduce_html 简化页面]
    B --> C[/find_similar_group 找重复元素]
    C --> D[/extract_fields_by_group 分组提取]
    D --> E[/extract_pagination_info 分析分页]
    E --> F[整合结果]
```

## ⚡ 性能优化技巧

### 1. 预处理HTML
```python
# 对于大型HTML，先进行精简
response = requests.post("/reduce_html", json={"data": large_html})
simplified_html = response.json()["data"]["truncate_html"]

# 再进行字段提取
response = requests.post("/extract_fields", json={
    "data": simplified_html,
    "fields": ["标题", "价格"]
})
```

### 2. 批量字段提取
```python
# 一次性提取多个字段，而不是分别请求
fields = ["标题", "价格", "描述", "图片", "链接", "评价"]
response = requests.post("/extract_fields", json={
    "data": html_content,
    "fields": fields
})
```

### 3. 合理使用模式
- **XPath模式**: 更精确，适合复杂结构
- **CSS选择器模式**: 更简洁，适合简单结构

## 🚨 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查参数格式和必填项 |
| 500 | 服务器内部错误 | 检查HTML格式，简化内容 |
| 422 | 数据验证失败 | 确保data字段不为空 |

### 错误响应示例
```json
{
    "code": 500,
    "message": "Bad Request due to Invalid HTML format",
    "data": []
}
```

## 🔧 高级配置

### 启用自动修正
```python
# 启用自动修正功能，提高提取准确性
response = requests.post("/extract_fields", 
    json={...},
    params={"enable_refine": True}
)
```

### 多语言支持
```python
# 支持多种语言界面
languages = ["zh_CN", "en_US", "ja_JP", "es_ES", "de_DE", "fr_FR", "it_IT", "ko_KR"]
```

## 📊 最佳实践

1. **从简单开始**: 先用小段HTML测试，确保理解接口用法
2. **逐步复杂化**: 逐渐处理更复杂的页面结构
3. **验证结果**: 始终验证提取结果的准确性
4. **善用对话**: 遇到问题时使用AI对话功能
5. **缓存结果**: 对于相同结构的页面，可以复用提取规则
