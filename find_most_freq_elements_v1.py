from bs4 import BeautifulSoup
from collections import defaultdict
from rich.progress import track

def find_similar_elements(html):
    soup = BeautifulSoup(html, 'html.parser')
    elements = defaultdict(list)
    
    # 遍历所有元素，同时记录其祖先链和深度
    element_info = {}
    for element in track(soup.find_all(True), description="find similar elements"):  # True表示找到所有标签
        #key = (element.name, frozenset(element.attrs.items()))
        cl_attr = element.get("class", "")
        if isinstance(cl_attr, list):
            cl_attr = " ".join(cl_attr)
        key = (element.name, cl_attr)
        #print(key,type(cl_attr))
        # 计算当前元素的深度并记录
        depth = 0
        ancestors = []
        parent = element
        while parent.parent:
            ancestors.append(parent.parent)
            parent = parent.parent
            depth += 1
            
        # 将信息存储在字典中
        element_info[element] = {'depth': depth, 'ancestors': ancestors}
        elements[key].append(element)
    
    return elements, element_info

def find_common_ancestor(elements, element_info):
    # 取第一元素的祖先链作为初始比较基础
    common_ancestors = element_info[elements[0]]['ancestors']
    
    # 比较其他元素的祖先链，找到最浅的公共祖先
    for element in elements[1:]:
        ancestors = element_info[element]['ancestors']
        common_ancestors = [anc for anc in common_ancestors if anc in ancestors]
        if not common_ancestors:
            break
    
    return common_ancestors[0] if common_ancestors else None

def main(html):
    similar_elements, element_info = find_similar_elements(html)
    result = {}
    
    for key, elements in track(similar_elements.items(), description="find common ancestor"):
        if len(elements) > 1:  # 如果存在多个相似元素
            common_ancestor = find_common_ancestor(elements, element_info)
            if common_ancestor:
                depth = element_info[common_ancestor]['depth']
                match_elements_num = len(elements)
                result[key] = (match_elements_num, depth)
    
    return result

# 示例HTML代码
with open("html_docs/goodlist.html", "r") as f:
    html = f.read()

# 执行代码
result = main(html)
for key, depth in result.items():
    print(f"Similar elements: {key}, Common ancestor depth: {depth}")
