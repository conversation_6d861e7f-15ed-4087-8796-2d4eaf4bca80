FROM harbor.ops.skieer.com/cached_docker_hub/library/python:3.10-buster

# 安装 Linux 软件。注意这里的 Linux 发行版为 Debian 10 ，代号为 buster
RUN echo 'deb https://mirrors.cloud.tencent.com/debian/ buster main contrib non-free\n\
deb https://mirrors.cloud.tencent.com/debian/ buster-updates main contrib non-free\n\
deb-src https://mirrors.cloud.tencent.com/debian/ buster main contrib non-free\n\
deb-src https://mirrors.cloud.tencent.com/debian/ buster-updates main contrib non-free\n\
' > /etc/apt/sources.list && \
    apt update && \
    apt install -y build-essential wget && \
    apt clean && \
    rm -rf /var/lib/apt/lists/*

# 安装 tini
RUN wget https://nexus.ops.skieer.com/repository/raw-group-public/tini/tini-v0.19.0 -O /usr/bin/tini && \
    chmod +x /usr/bin/tini

# 配置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 设置环境变量
ENV USER=skieer \
    USER_ID=1000 \
    WORK_DIR=/app \
    TZ=Asia/Shanghai

# 创建非 root 用户
RUN useradd $USER -u $USER_ID -m -s /bin/bash && \
    mkdir -p $WORK_DIR && \
    chown -R $USER:$USER $WORK_DIR

# 设置工作目录
WORKDIR $WORK_DIR

# 安装 Python 依赖包
RUN --mount=type=secret,id=.netrc,dst=/root/.netrc \
    pip install poetry --no-cache-dir --index-url=https://nexus.ops.skieer.com/repository/pypi-group/simple
COPY pyproject.toml poetry.lock .
RUN --mount=type=secret,id=.netrc,dst=/root/.netrc \
    poetry config virtualenvs.create false && \
    poetry install --no-interaction --no-root

# 切换到非 root 用户
USER $USER

# 拷贝代码目录
COPY --chown=$USER:$USER  .  .
RUN chmod +x startup.sh

# 设置启动命令
ENTRYPOINT ["tini", "--", "/app/startup.sh"]
