"""
基于CrewAI多Agent系统的API服务
保持与原有API接口完全兼容，但内部使用多Agent协作
"""

import re
import time
from typing import List, Literal

import tiktoken
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, field_validator

from core.crew import get_crew_instance
from core.utils.token_calculator import truncate_text_tokens
from run_chat import router as chat_router

app = FastAPI(
    title="Chat4Data CrewAI API",
    description="基于CrewAI多Agent系统的智能网页元素分析API",
    version="2.0.0"
)
app.include_router(chat_router)

# 允许跨域的源
origins = ["*"]

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 获取全局Crew实例
crew = get_crew_instance()


# 定义请求模型
class DataRequest(BaseModel):
    data: str
    fields: List[str] = []
    mode: Literal["xpath", "selector"] = "xpath"
    language: Literal[
        "zh_CN", "en_US", "ja_JP", "es_ES", "de_DE", "fr_FR", "it_IT", "ko_KR"
    ] = "zh_CN"

    @field_validator("data")
    def check_not_empty(cls, v):
        if not v.strip():
            raise ValueError("The 'data' field cannot be empty or just whitespace.")
        return v


class FieldExtraction(BaseModel):
    name: str = ""
    data: str
    fields: List[str] = []
    groups: List[str] = []
    mode: Literal["xpath", "selector"] = "xpath"
    language: Literal[
        "zh_CN", "en_US", "ja_JP", "es_ES", "de_DE", "fr_FR", "it_IT", "ko_KR"
    ] = "zh_CN"

    @field_validator("data")
    def check_not_empty(cls, v):
        if not v.strip():
            raise ValueError("The 'data' field cannot be empty or just whitespace.")
        return v


@app.post("/analyze_html", description="分析网页主要结构")
async def process_webpage(request: DataRequest, enable_refine: bool = True):
    """使用CrewAI多Agent系统分析网页结构"""
    try:
        # 预处理HTML（如果太长则截断）
        html_content = request.data
        if len(html_content) > 500000:  # 如果HTML太长，先截断
            html_content = truncate_text_tokens(
                html_content, max_tokens=200000, encoding_name="o200k_base"
            )
        
        # 调用CrewAI多Agent系统
        result = crew.analyze_html_structure(
            html_content=html_content,
            mode=request.mode,
            language=request.language,
            fields=request.fields,
            enable_refine=enable_refine
        )
        
        if result["success"]:
            response = {
                "code": 200,
                "message": "Request successful",
                "data": result["data"],
                "metadata": {
                    "agents_used": result["metadata"]["agents_used"],
                    "crew_system": "CrewAI v2.0"
                }
            }
        else:
            response = {
                "code": 500,
                "message": f"Analysis failed: {result['error']}",
                "data": []
            }
            
    except Exception as e:
        import traceback
        traceback.print_exc()
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    
    return response


@app.post("/extract_fields", description="分析给定页面的主要字段")
async def extract_fields(request: FieldExtraction, enable_refine: bool = True):
    """使用CrewAI多Agent系统提取字段"""
    try:
        # 预处理HTML
        html_content = request.data
        if len(html_content) > 500000:
            html_content = truncate_text_tokens(
                html_content, max_tokens=120000, encoding_name="o200k_base"
            )
        
        # 调用CrewAI多Agent系统
        result = crew.extract_fields(
            html_content=html_content,
            fields=request.fields,
            mode=request.mode,
            language=request.language,
            name=request.name,
            groups=None,
            enable_refine=enable_refine
        )
        
        if result["success"]:
            response = {
                "code": 200,
                "message": "Request successful",
                "data": result["data"],
                "metadata": {
                    "agents_used": result["metadata"]["agents_used"],
                    "crew_system": "CrewAI v2.0"
                }
            }
        else:
            response = {
                "code": 500,
                "message": f"Extraction failed: {result['error']}",
                "data": []
            }
            
    except Exception as e:
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    
    return response


@app.post("/extract_fields_by_group", description="分析给定页面的主要字段和区域")
async def extract_fields_by_group(request: FieldExtraction, enable_refine: bool = True):
    """使用CrewAI多Agent系统分组提取字段"""
    try:
        # 预处理HTML
        html_content = request.data
        if len(html_content) > 500000:
            html_content = truncate_text_tokens(
                html_content, max_tokens=120000, encoding_name="o200k_base"
            )
        
        # 调用CrewAI多Agent系统
        result = crew.extract_fields(
            html_content=html_content,
            fields=request.fields,
            mode=request.mode,
            language=request.language,
            name=request.name,
            groups=request.groups,
            enable_refine=enable_refine
        )
        
        if result["success"]:
            response = {
                "code": 200,
                "message": "Request successful",
                "data": result["data"],
                "metadata": {
                    "agents_used": result["metadata"]["agents_used"],
                    "crew_system": "CrewAI v2.0"
                }
            }
        else:
            response = {
                "code": 500,
                "message": f"Extraction failed: {result['error']}",
                "data": []
            }
            
    except Exception as e:
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    
    return response


@app.post("/extract_pagination_info", description="分析给定分页器html的xpath字段")
async def extract_pagination_info(request: DataRequest, enable_refine: bool = True):
    """使用CrewAI多Agent系统分析分页控件"""
    try:
        # 调用CrewAI多Agent系统
        result = crew.analyze_pagination(
            html_content=request.data,
            mode=request.mode,
            language=request.language,
            enable_refine=enable_refine
        )
        
        if result["success"]:
            response = {
                "code": 200,
                "message": "Request successful",
                "data": result["data"],
                "metadata": {
                    "agents_used": result["metadata"]["agents_used"],
                    "crew_system": "CrewAI v2.0"
                }
            }
        else:
            response = {
                "code": 500,
                "message": f"Pagination analysis failed: {result['error']}",
                "data": []
            }
            
    except Exception as e:
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": []}
    
    return response


@app.post("/reduce_html", description="精简页面元素")
async def reduce_html(request: DataRequest):
    """使用CrewAI多Agent系统精简HTML"""
    try:
        # 调用CrewAI多Agent系统
        result = crew.optimize_html(
            html_content=request.data,
            operation="reduce",
            mode=request.mode,
            language=request.language
        )
        
        if result["success"]:
            response = {
                "code": 200,
                "message": "Request successful",
                "data": result["data"],
                "metadata": {
                    "agents_used": result["metadata"]["agents_used"],
                    "crew_system": "CrewAI v2.0"
                }
            }
        else:
            response = {
                "code": 500,
                "message": f"HTML optimization failed: {result['error']}",
                "data": {}
            }
            
    except Exception as e:
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": {}}
    
    return response


@app.post("/find_similar_group", description="查找相似元素组")
async def find_similar_group(request: DataRequest):
    """使用CrewAI多Agent系统查找相似元素组"""
    try:
        # 调用CrewAI多Agent系统
        result = crew.optimize_html(
            html_content=request.data,
            operation="find_similar",
            mode=request.mode,
            language=request.language
        )
        
        if result["success"]:
            response = {
                "code": 200,
                "message": "Request successful",
                "data": result["data"],
                "metadata": {
                    "agents_used": result["metadata"]["agents_used"],
                    "crew_system": "CrewAI v2.0"
                }
            }
        else:
            response = {
                "code": 400,
                "message": f"Similar group analysis failed: {result['error']}",
                "data": {}
            }
            
    except Exception as e:
        response = {"code": 400, "message": f"Bad Request due to {e}", "data": {}}
    
    return response


@app.post("/get_token_counts", description="计算token数")
async def count_token(request: DataRequest):
    """计算token数量（保持原有逻辑）"""
    html_text = request.data
    try:
        encoding = tiktoken.get_encoding("o200k_base")
        tokens = len(encoding.encode(html_text))
        response = {
            "code": 200,
            "message": "Request successful",
            "data": {"char_length": len(html_text), "model_tokens": tokens},
        }
    except Exception as e:
        response = {"code": 500, "message": f"Bad Request due to {e}", "data": {}}
    return response


@app.get("/", description="API状态检查")
async def root():
    """API根路径，显示系统状态"""
    return {
        "message": "Chat4Data CrewAI Multi-Agent System",
        "version": "2.0.0",
        "system": "CrewAI",
        "agents": [
            "HTML Structure Analyst",
            "Field Extraction Expert", 
            "Pagination Analyst",
            "HTML Optimizer",
            "Quality Validator",
            "Chat Assistant"
        ],
        "status": "running"
    }


@app.get("/health", description="健康检查")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": time.time()}


# 运行应用
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("run_api_crewai:app", host="0.0.0.0", port=18603, workers=2)
