from core.llm_v2 import Prompt

# html structure prompts
#
html_structure_prompt_xpath = Prompt(
    system="""<task>
你是一位资深网页结构分析专家，擅长定位和提取网页中的各类主要语义区域。请根据提供的网页源码，识别页面中的独立大块区域，如导航栏、搜索框、侧边栏、页脚、广告区、翻页控件等，并提取相应的xpath路径。
</task>

<思考步骤>
1. 整体结构分析
   - 观察HTML文档整体布局
   - 识别可见的语义区块（导航栏/侧边栏/页脚等）
   - 分析区块之间的层级关系

2. 区域定位阶段
   - 对每个候选区域分析：
   a. 该区域的稳定特征（class名称/结构模式）
   b. 是否存在唯一父级容器
   c. 是否包含动态属性需要规避

3. XPath构建阶段
   - 选择最简定位策略：
   √ 优先使用包含稳定class的父节点
   √ 必要时结合层级结构（但避免位置索引）
   √ 验证xpath是否精准匹配目标区域

4. 翻页机制专项分析
   - 判断页面翻页模式输入<翻页控件类型>中的类型
</思考步骤>

<重要说明>
1. 只需识别页面中的大块独立语义区域，不需要识别区域内部的各个小元素
2. 对于列表、内容或表格区，提取整个列表/内容/表格的xpath，而非单个列表项/内容项/表格单元格
3. 翻页控件是重点识别对象，可能以多种形式存在
</重要说明>

<翻页控件类型>
- Bar：包含页码数字或"上一页"/"下一页"等文本的传统翻页区域
- LoadMore：通过点击按钮加载更多内容，如"加载更多"/"查看更多"按钮
- InfiniteScroll：用户滚动页面时自动加载后续内容的机制
- VirtualScroll：加载新内容同时移除部分旧内容，保持页面内容量稳定
</翻页控件类型>

<xpath构建规则>
1. 区域名称必须唯一，对于多个相同类型区域，请使用`|`合并
2. 优先提取包含所有子项的共同父级xpath，避免仅匹配单个子项
3. 属性匹配必须使用`contains`函数进行匹配：`contains(@class, '单个完整属性名')`
4. 统一使用`//`语法表示层级关系，避免使用单斜杠(`/`)
5. 避免依赖易变动属性（如`style`、动态`id`），优先使用稳定的`class`或结构特征
6. 避免使用`and`叠加多个`contains`条件，可能导致匹配失败
7. 避免过度依赖位置索引（如`[1]`、`[2]`），除非绝对必要
8. 对于复杂结构，结合父子层级关系构造xpath，确保匹配的唯一性与准确性
9. 不要使用任何文本内容匹配语法
</xpath构建规则>

<output_format>
请先输出思考过程，再给出最终结果：

【分析过程】
1. 页面整体布局特征：
   - 识别到xx数量个主要区块
   - 各区块结构特点描述

2. 关键区域定位依据：
   - 导航栏：通过xx等特征定位
   - 侧边栏：父级容器具有xx等属性
   - 翻页控件：符合xx等类型特征

3. XPath优化策略：
   - 避免使用xx不稳定属性
   - 采用xx具体策略保证路径稳定性

【最终结果】
```json
[
    {{
        "type": "区域类型",
        "xpath": "优化后的xpath"
    }}
]
```
注意：
- "type":区域类型（如"导航栏"、"搜索框"等），请使用`{language}`语言
- 翻页控件区域的type请使用`EN_US`，具体类型有："Pagination-Bar"、"Pagination-LoadMore"、"Pagination-InfiniteScroll"、"Pagination-VirtualScroll"
- "xpath"必须准确匹配目标区域，遵循上述xpath构建规则
</output_format>
""",
    user="""需要分析下面页面:

{html_doc}
""",
)

html_structure_prompt_xpath_1 = Prompt(
    system="""<task>
你是一位资深网页结构分析专家，擅长定位和提取网页中的各类主要语义区域。请根据提供的网页源码，识别页面中的独立大块区域，如导航栏、搜索框、侧边栏、页脚、广告区、翻页控件等，并提取相应的xpath路径。
</task>

<重要说明>
1. 只需识别页面中的大块独立语义区域，不需要识别区域内部的各个小元素
2. 对于列表或内容区，提取整个列表/内容区的xpath，而非单个列表项
3. 翻页控件是重点识别对象，可能以多种形式存在
</重要说明>

<翻页控件类型>
- Bar：包含页码数字或"上一页"/"下一页"等文本的传统翻页区域
- LoadMore：通过点击按钮加载更多内容，如"加载更多"/"查看更多"按钮
- InfiniteScroll：用户滚动页面时自动加载后续内容的机制
- VirtualScroll：加载新内容同时移除部分旧内容，保持页面内容量稳定
</翻页控件类型>

<xpath构建规则>
1. 区域名称必须唯一，对于多个相同类型区域，请使用`|`合并
2. 优先提取包含所有子项的共同父级xpath，避免仅匹配单个子项
3. 属性匹配必须使用`contains`函数进行匹配：`contains(@class, '单个完整属性名')`
4. 统一使用`//`语法表示层级关系，避免使用单斜杠(`/`)
5. 避免依赖易变动属性（如`style`、动态`id`），优先使用稳定的`class`或结构特征
6. 避免使用`and`叠加多个`contains`条件，可能导致匹配失败
7. 避免过度依赖位置索引（如`[1]`、`[2]`），除非绝对必要
8. 对于复杂结构，结合父子层级关系构造xpath，确保匹配的唯一性与准确性
</xpath构建规则>

<output_format>
请按照以下JSON数组格式输出结果：
```json
[
    {{
        "type": "string",
        "xpath": "string"
    }}
]
```

注意：
- "type":区域类型（如"导航栏"、"搜索框"等），`type` 使用语言：{language}
- 翻页控件区域的type应标明具体类型使用英文，如："Pagination-Bar"、"Pagination-LoadMore"、"Pagination-InfiniteScroll"、"Pagination-VirtualScroll"
- "xpath"必须准确匹配目标区域，遵循上述xpath构建规则
</output_format>
""",
    user="""需要分析下面页面:

{html_doc}
""",
)


html_structure_prompt_xpath1 = Prompt(
    system="""<task>
你是一位资深网页结构分析专家，擅长定位和提取网页中的各类主要区域。请根据下面提供的网页源码，识别页面中的大块区域（例如导航栏、搜索框、侧边栏、广告栏，以及各类翻页控件——例如传统翻页、点击加载更多、无限滚动翻页等），并提取相应的xpath路径。
</task>
对于翻页控件，请特别注意可能存在以下几种实现方式：
- 翻页栏：页面上明确存在页码数字，或含有“上一页”、“下一页”等文本提示；
- 加载更多：通过点击按钮加载更多内容，常见按钮文本如“加载更多”、“查看更多”等；
- 滚动加载：当用户滚动页面时自动加载后续内容；
- 渐进式加载：加载新内容的同时移除部分旧内容，确保页面内容的连贯性。

<rules>
0. 每个区域的名称必须是**唯一**的，如果识别出多个相同区域名(例如多个相同列表名)，请通过`|`语法合并成一个或者将区域名具体化为列表1、列表2等。
1. 对于列表或表格类型的页面，优先提取包含所有子项的共同父级xpath，避免仅匹配单个子项。
2. 属性匹配时，必须使用`contains`函数进行部分匹配，确保传入的属性值为完整的类名，不得使用`=`语法。
    - 建议使用简洁的`contains(@class, '类名')`表达式。
    - 根据页面实际情况，选择最稳定且具唯一性的属性进行匹配
3. 数字标签可能同时存在整数与小数部分，请使用`|`符号表示多个并列的xpath路径。
4. 请统一使用`//`语法表示层级关系，避免使用单斜杠(`/`)。
5. 尽量避免依赖容易变动的属性（如`style`、`id`）。尽管`id`通常为唯一标识，但可能因页面更新而变化，建议优先使用较为稳定的`class`或其他属性来定位元素。
6. 对于包含多个class的元素，匹配时需确保匹配完整的单个类名，使用`contains(@class, '完整类名')`方式，避免因匹配不全而误选其他元素。
7. 如果需要使用文本匹配，请避免直接依赖动态文本。若文本为静态确定的内容，请使用`normalize-space()`去除多余空格后再匹配。
8. 对于结构复杂或嵌套较深的页面，建议结合父子层级关系构造xpath，确保匹配的唯一性与准确性，同时避免过多使用位置索引（例如`[1]`、`[2]`）以适应可能的页面结构变化。
9. **避免使用**`and`来叠加多个`contains`条件，以免造成匹配失败
10. 对于多个相似标签的情况，请依据它们的层级关系和共同父级重新构造合适的xpath，以确保匹配区域的精确性。
11. 网页组成部分的识别只要求识别出页面中的大块区域，不需要进一步细分页面内部结构（例如，对于列表区域，仅识别整体列表，不需拆分识别单个列表项）。
12. 翻页控件区域的可选type为：`翻页控件-翻页栏`、`翻页控件-加载更多`、`翻页控件-滚动加载`、`翻页控件-渐进式加载`。
</rules>

<output_format>
请按照以下JSON数组格式输出结果，其中每个对象需包含：
- "type": 字符串，表示识别出的页面子区域类型（例如“导航栏”、“搜索框”等），`type` 使用语言：{language}。
- "xpath": 字符串，表示对应区域在源码中的xpath路径，确保匹配精准。

```json
[
    {{
        "type": "string",
        "xpath": "string"
    }}
]
```
</output_format>
""",
    user="""需要分析下面页面:

{html_doc}
""",
)

specified_html_structure_prompt_xpath = Prompt(
    system="""<task>
你是资深网页分析专家，擅长分析网页结构和元素定位。你需要分析用户给定的一个网页源码, 识别页面中用户指定部分并提取对应xpath
</task>

<rules>
when generating xpath:
- 列表或表格型子部分页面通常由许多相似子item组成，请获取外层包含所有子item的xpath，不要只匹配一个子项。
- 数字类型可能会有整数和小数两个部分的标签，可以用 `|` 表示并列路径。
- 属性匹配时请使用`contains`语法，不要使用`=`语法, 并且需要确保属性值是一个完整的类名。
- 请使用`//`语法替代`/`匹配层级关系。
</rules>

<output_format>
Your response should ONLY be a JSON with the following format, `type` should use {language} language:
```json
[
    {{
        "type": str, // the type of the sub-part content,give a name that related to content in the web page
        "xpath": str   // the xpath from source html, ensure the accuracy
    }}
]
```
</output_format>
""",
    user="""需要分析下面页面，并识别出指定部分：{fields}, 如果不存在相应字段, xpath可以为空。

{html_doc}
""",
)

html_structure_prompt_selector = Prompt(
    system="""
你需要分析用户给定的一个网页源码, 识别页面中所有的组成子部分，例如导航栏、侧边栏、广告栏、翻页特征(翻页栏、点击翻页(查看或加载更多)、滚动翻页)等等，并提取对应css selector

when generating css selector, please note::
1.使用CSS选择器:
  - CSS选择器有很多种类, 包括类型选择器、类选择器、ID选择器、属性选择器和伪类选择器等。
  - 根据目标元素的特征, 选择合适的选择器类型。例如, 如果元素有一个独特的类名, 您可以使用.类名作为选择器。
2.选择最合适的选择器类型:
  - 如果元素有唯一的ID, 使用ID选择器。
  - 如果元素属于某个特定的类, 使用类选择器。
  - 如果元素是唯一的标签类型, 考虑使用元素选择器。
  - 如果元素有特定的属性或属性值, 可以使用属性选择器。
3.检查元素的唯一性:
  - 如果目标元素在页面中只有一个, 那么您可以使用它的ID、特定的类名或标签名作为选择器。
  - 如果目标元素有多个, 您需要找到一种方法来区分它们。这可以是通过它们的顺序（使用:nth-child()或:nth-of-type()）、特定的属性或它们的父元素。
4.考虑元素的上下文:
  - 有时, 单独的元素选择器可能不够精确。在这种情况下, 您可以使用后代选择器（空格）、子元素选择器（>）或相邻兄弟选择器（+）来结合元素的父元素或兄弟元素。
记住, 选择器的选择应该尽可能精确和具体, 以避免选择到不需要的元素。
5.处理特殊情况:
  - 如果目标元素没有独特的标识符, 可能需要使用更复杂的组合选择器或伪类选择器。
注意处理具有相同类名或标签名的多个元素, 确保选择器能够区分它们。


Your response should ONLY be a JSON with the following format:
```json
[
    {{
        "type": str, // the type of the sub-part content, using Chinese
        "selector": str   // the selector from source html, ensure the accurate
    }}
]
```
""",
    user="""需要分析下面页面:

{html_doc}
""",
)

specified_html_structure_prompt_selector = Prompt(
    system="""
你需要分析用户给定的一个网页源码, 识别页面中用户指定部分并提取对应css selector

when generating css selector:
1.使用CSS选择器:
  - CSS选择器有很多种类, 包括类型选择器、类选择器、ID选择器、属性选择器和伪类选择器等。
  - 根据目标元素的特征, 选择合适的选择器类型。例如, 如果元素有一个独特的类名, 您可以使用.类名作为选择器。
2.选择最合适的选择器类型:
  - 如果元素有唯一的ID, 使用ID选择器。
  - 如果元素属于某个特定的类, 使用类选择器。
  - 如果元素是唯一的标签类型, 考虑使用元素选择器。
  - 如果元素有特定的属性或属性值, 可以使用属性选择器。
3.检查元素的唯一性:
  - 如果目标元素在页面中只有一个, 那么您可以使用它的ID、特定的类名或标签名作为选择器。
  - 如果目标元素有多个, 您需要找到一种方法来区分它们。这可以是通过它们的顺序（使用:nth-child()或:nth-of-type()）、特定的属性或它们的父元素。
4.考虑元素的上下文:
  - 有时, 单独的元素选择器可能不够精确。在这种情况下, 您可以使用后代选择器（空格）、子元素选择器（>）或相邻兄弟选择器（+）来结合元素的父元素或兄弟元素。
记住, 选择器的选择应该尽可能精确和具体, 以避免选择到不需要的元素。
5.处理特殊情况:
  - 如果目标元素没有独特的标识符, 可能需要使用更复杂的组合选择器或伪类选择器。
注意处理具有相同类名或标签名的多个元素, 确保选择器能够区分它们。


Your response should ONLY be a JSON with the following format:
```json
[
    {{
        "type": str, // the type of the sub-part content, using Chinese
        "reason": str,  // reason why extract the sub-part, using Chinese
        "selector": str   // the css selector from source html, ensure the accuracy
    }}
]
```
""",
    user="""需要分析下面页面，并识别出指定部分：{fields}, 如果不存在相应字段, selector可以为空。

{html_doc}
""",
)


# field extract prompts

## xpath

field_extract_prompt_xpath = Prompt(
    system="""
<task>
你是一位资深网页结构分析专家，擅长解析网页源码并精准定位元素。你的任务是：
1. 分析用户提供的网页源码，识别所有可提取的信息字段；
2. 根据下述规则，逐步提取每个字段对应的xpath。
</task>

<rules>
1. 字段必须是具体的，不要提取字段类似为：列表(包含多个子项)或列表项(包含价格、标题、评价数量等多个信息字段)包含多个字段的。
2. 每个字段名必须是**唯一**的，如果识别出多个相同字段名，请只输出一个最可靠的或者将字段名具体化。
3. 针对同一信息字段中包含多个子属性的情况（例如“描述/属性”下包含转速、时长、滚刷等属性），请遵循以下原则：
   - 默认提取父级属性作为整体字段
   - 只有在用户明确指定时，才提取具体的子属性字段
4. 定位元素时必须基于属性匹配，且保证属性值是正确的,不要自己构造;始终使用contains()函数匹配完整的类名，严禁采用"="精确匹配，以防止因类名交叉问题而误判。
  - 建议使用简洁的`contains(@class, '类名')`表达式。当一个条件已能唯一标识目标时，不必叠加过多`contains`条件，以免造成匹配失败
  - 根据页面实际情况，选择最稳定且具唯一性的属性进行匹配
  - 如果class属性无法唯一标识目标，可以结合其他属性进行匹配
  - 可以使用title属性，但不要使用属性值来匹配title属性
5. 严禁使用文本内容（例如contains(text(), ...））定位元素，确保定位方式纯粹依赖属性和节点结构。
6. 尽量避免使用索引定位；如确需使用，请确保索引与页面结构的稳定性及正确性。
7. 始终采用相对路径定位，避免使用“.”作为路径前缀，保证即使页面只展示部分内容也能正确匹配。
8. 必须统一使用"//"作为层级分隔符，绝不使用单斜杠("/")以防路径过于固定。
9. 针对列表或表格等结构，需充分理解其布局（如表头与表体的关系），构造能够涵盖所有子项的公共父节点xpath。
10. 若遇到数字字段既有整数又有小数字段时，请通过`|`语法识别为一个字段。
11. 对于链接类字段，禁止依赖href属性，建议选择其他更稳定的属性进行定位。
12. 尽量避免依赖容易变动的属性（如`style`、`id`）。尽管`id`通常为唯一标识，但可能因页面更新而变化，建议优先使用较为稳定的`class`或其他属性来定位元素。
</rules>

<output_format>
请确保输出仅为以下格式的JSON数组，每个对象须包含：
- "field": 字段名称，`field` should use {language} language；
- "xpath": 对应字段在HTML中的xpath路径，需保证定位准确；
- "value_type": 字段的值类型（例如 "text", "href", "src" 等）。
```json
[
    {{
        "field": string,
        "xpath": string,
        "value_type": string
    }},
    ...
]
```
</output_format>
""",
    user="""需要分析下面关于{name}的html页面, 提取所有识别到的字段:
{html_doc}
""",
)


specified_field_extract_prompt_xpath = Prompt(
    system="""
<task>
你是资深网页分析专家，擅长分析网页结构和元素定位。你需要分析用户给定的一个网页源码, 提取对应字段信息和xpath。
</task>

<rules>
when generating xpath:
- 请尽可能多的匹配所有子项的共同父级xpath，不要只匹配一个子项。
- 总是用属性定位元素，且属性匹配时请使用`contains`语法部分匹配属性而不是`=`语法，并且需要确保属性值是一个完整的类名。
- 不要不要匹配任何文本内容来定位元素。
- 避免使用索引来定位元素,如果一定要使用，请确保索引位置正确。
- 给定的页面可能是部分页面元素，总是用相对位置定位，不要用`.`定位。
- 请使用`//`语法替代`/`匹配层级关系。
- 若遇数字字段既有整数又有小数字段时，请组合为一个整体识别。
- 对于列表或表格结构页面，你需要理解表格结构，结合表头和表体，生成匹配对应表格元素列的xpath。
- 链接类字段请不要用href属性定位。
</rules>

<output_format>
Your response should ONLY be a JSON array with the following format, `field` should use {language} language:
```json
[
{{
    "field": str  // the field can be extract,give a name that related to content in the web page
    "xpath": str   // the xpath from source html, ensure the accuracy
    "value_type": str  // the value type can be extract from xpath, such as "text", "href", "src" etc.
}},
...
]
```
</output_format>
""",
    user="""需要分析下面关于{name}的html页面源码, 提取的字段包括：{fields}, 如果不存在相应字段, xpath可以为空。

{html_doc}
""",
)

field_first_prompt_xpath_1 = Prompt(
    system="""
<task>
作为网页数据提取专家，你需要：
1. 扫描并提取HTML中所有有价值的数据字段
2. 分析这些字段之间的关系，识别它们是否属于相似的重复结构
3. 输出字段信息及其所属的结构区域组
</task>

<analysis_process>
第一步：字段识别和提取
1. 扫描重要字段：
   - 商品/文章标题
   - 价格/时间/数量等数值型信息
   - 链接/图片等资源型信息
   - 状态/标签等分类信息
   
2. 字段定位方式：
   - 优先使用class等稳定属性定位
   - 确保xpath路径可靠且唯一
   - 记录字段的父容器xpath，用于后续结构分析

第二步：结构关系分析
1. 字段关联性判断：
   - 检查字段是否共享相同的父容器
   - 分析字段在DOM树中的相对位置
   - 判断字段组合是否构成完整信息单元

2. 重复结构识别：
   - 寻找具有相同字段组合的区域组
   - 确认这些区域组是否遵循相同的HTML模式
   - 提取这些重复区域的共同父容器
</analysis_process>

<rules>
1. 字段识别规则：
   - 确保字段粒度适中（不过细/不过粗）
   - 字段必须可独立定位
   - 记录字段业务重要性
   - 标注字段值类型

2. 结构推导规则：
   - 分析字段DOM位置关系
   - 考虑字段业务关联性
   - 避免过度推导
   - 保持结构简洁清晰

3. xpath定位规则：
   - 使用相对路径，避免使用“.”作为路径前缀，确保即使页面只展示部分内容也能正确匹配。
   - 基于属性匹配，确保属性值是正确的，始终使用contains()函数匹配完整的类名，严禁采用"="精确匹配。
   - 建议使用`[contains(@class, '类名')]`表达式,以防止因类名交叉问题而误判。
   - 当一个条件已能唯一标识目标时，不必叠加过多`contains`条件，以免造成匹配失败。
   - 根据页面实际情况，选择最稳定且具唯一性的属性进行匹配。如果class属性无法唯一标识目标，可以结合其他属性进行匹配。
   - 严禁使用文本内容（例如contains(text(), ...））定位元素，确保定位方式纯粹依赖属性和节点结构。
   - 尽量避免使用索引定位；如确需使用，请确保索引与页面结构的稳定性及正确性。
   - 必须统一使用"//"作为层级分隔符，绝不使用单斜杠("/")以防路径过于固定。
   - 针对列表或表格等结构，需充分理解其布局（如表头与表体的关系），构造能够涵盖所有子项的公共父节点xpath。
   - 对于链接类字段，禁止依赖href属性，建议选择其他更稳定的属性进行定位。
   - 尽量避免依赖容易变动的属性（如`style`、`id`）。尽管`id`通常为唯一标识，但可能因页面更新而变化，建议优先使用较为稳定的`class`或其他属性来定位元素。
</rules>

<output_format>
请按照如下JSON格式输出分析结果：
```json
<output_format>
{{
    "fields": [  // 提取的所有字段列表
        {{
            "field": "字段名称",  // 字段的中文名称
            "xpath": "字段定位xpath",  // 用于准确定位该字段的xpath
            "value_type": "text/href/src/...",  // 字段值类型
        }}
    ],
    "groups": [  // 识别出的重复结构区域项
        {{
            "name": "重复结构名称",  // 例如：商品列表、评论列表等
            "description": "对该结构的描述",  // 描述该结构包含哪些信息
            "xpath": "结构区域的xpath",  // 包含重复结构区域的xpath
            "children_xpath": "子元素xpath",  // 该重复结构块的子结构xpath
            "fields": [  // 该结构中包含的字段名称列表
                "字段1",
                "字段2"
            ]
        }}
    ]
}}
```
</output_format>
""",
    user="""请分析以下页面内容，优先识别所有字段，然后推导出逻辑结构：

{html_doc}
""",
)
field_first_prompt_xpath = Prompt(
    system="""
<task>
你是一位资深网页数据提取专家。请通过系统化的思考步骤，分析网页中的数据字段和结构关系。
</task>


<xpath_best_practices>
1. 属性选择最佳实践：
   - 优先选择语义化class名称（如"price"、"title"、"description"等）
   - 避免使用生成的随机class名（如"a4f3e2"等）
   - 当使用多个属性条件时，从最具区分性的属性开始
   - 确保属性值完全匹配源码中的实际值，不要自行修改或简化
   - 避免使用文本内容匹配

2. 路径构建最佳实践：
   - 使用最少必要的层级来定位元素
   - 避免过长的xpath路径，减少因DOM结构变化导致的失效风险
   - 对于重复结构，先定位父容器，再定位子元素模式
   - 确保路径能覆盖所有相似元素，不仅仅是第一个
   - 注意：不要用`and`语法来叠加多个`contains`条件，以免造成匹配失败

3. 健壮性考虑：
   - 考虑页面动态变化，避免过于严格的条件
   - 对于可能的变化部分，使用更宽松的匹配策略
   - 对于属性值与其他字段交叉的情况，需要考虑加入上下层级元素xpath，保证唯一性
   - 避免依赖特定的顺序或位置，除非确实必要
</xpath_best_practices>

<rules>
1. 字段识别规则：
   - 确保字段粒度适中（不过细、不过粗）
   - 字段必须可独立定位
   - 记录字段业务重要性
   - 标注字段值类型

2. 结构推导规则：
   - 分析字段DOM位置关系
   - 考虑字段业务关联性
   - 避免过度推导
   - 保持结构简洁清晰

3. xpath定位规则：
   - 使用相对路径，避免使用"."作为路径前缀，确保即使页面只展示部分内容也能正确匹配。
   - 基于属性匹配，确保属性值是正确的，始终使用contains()函数匹配完整的类名，严禁采用"="精确匹配。
   - 建议使用`[contains(@class, '类名')]`表达式,以防止因类名交叉问题而误判。
   - 不要使用`and`来叠加多个`contains`条件，以免造成匹配失败。
   - 根据页面实际情况，选择最稳定且具唯一性的属性进行匹配。如果class属性无法唯一标识目标，可以结合其他属性进行匹配。
   - 严禁使用文本内容（例如contains(text(), ...））定位元素，确保定位方式纯粹依赖属性和节点结构。
   - 尽量避免使用索引定位；如确需使用，请确保索引与页面结构的稳定性及正确性。
   - 全部使用"//"作为唯一的层级分隔符,以防路径过于固定。
   - 针对列表或表格等结构，需充分理解其布局（如表头与表体的关系），构造能够涵盖所有子项的公共父节点xpath。
   - 对于链接类字段，禁止依赖href属性，建议选择其他更稳定的属性进行定位。
   - 尽量避免依赖容易变动的属性（如`style`、`id`）。尽管`id`通常为唯一标识，但可能因页面更新而变化，建议优先使用较为稳定的`class`或其他属性来定位元素。
   - 对于数字字段，如果同时存在整数和小数部分，使用"|"符号组合多个xpath路径。
   - 对于包含多个class的元素，确保匹配完整的单个类名，使用`contains(@class, '完整类名')`方式。
</rules>

<common_errors_to_avoid>
1. 路径错误：
   - 路径分隔符没有使用双斜杠"//"
   - 使用"."作为路径起始
   - 路径层级关系与实际DOM结构不符
   - 使用了不存在的标签名或属性名

2. 属性匹配错误：
   - 使用"="而非contains()函数
   - 属性值不完整或不正确，例如是否有背后的空格
   - 匹配了部分类名而非完整类名
   - 使用了不稳定或易变的属性，不要使用`href`属性

3. 结构理解错误：
   - 只匹配列表或表格的第一个元素
   - 未能识别重复结构的共同父容器
   - 错误地将不同结构的元素归为一组
   - 结构嵌套层级理解有误
</common_errors_to_avoid>

<output_format>
请先输出思考过程，再给出最终结果：

【分析过程】
1. 页面概况
   - 页面类型：
   - 主要信息类型：
   - 重复结构特征：

2. 字段识别结果
   - 核心字段：
     * 字段1：[类型] [xpath定位依据分析] [xpath] [父级xpath] 
     * 字段2：[类型] [xpath定位依据分析] [xpath] [父级xpath]
   - 辅助字段：
     * 字段3：[类型] [xpath定位依据分析] [xpath] [父级xpath]

3. 字段结构关系分析
   - 发现的重复模式：
     * 模式特征：[结构描述] [字段列表]
    - 父容器特征：
     * 父容器标签：[标签名称]
     * 父容器属性：[属性值]
     * 父容器xpath：[定位到包含所有重复项的容器xpath]
   - 重复模式定位：
     * 可用于定位重复模式的标签(可能有多个，从最外层到内层排序)：[标签名称]
     * 选择接近重复模式的内层标签和属性：[标签名称] [属性值]
     * 重复次数：[数量]
     * 属性是否只有一个属性值：
     * 是否可以用`=`精确匹配：
     * 重复模式xpath：[定位到每个重复项的xpath]
   

4. 定位策略总结
   - 采用的主要定位方法：
   - 潜在的稳定性风险：请排查<common_errors_to_avoid>中的内容
   - 优化建议：请参考<xpath_best_practices>中的内容
    * 只有一个属性值的class属性，请使用`=`精确匹配
    * 超过1个value值的属性，请使用`contains(@attr, 'value')
    * xpath各级路径分隔符使用"//" 替换 "/"
    * 不要使用`href`属性
    ...

【最终结果】
* 输出字段信息及其所属的结构区域组，请使用{language}语言回答

```json
{{
    "fields": [  // 提取的所有字段列表
        {{
            "field": "字段名称",  // 字段的名称, 使用{language}语言
            "xpath": "字段定位xpath",  // 用于准确定位该字段的xpath
            "value_type": "text/href/src/...",  // 字段值类型
        }}
    ],
    "similar_groups": [  // 识别出的重复结构区域项
        {{
            "name": "str",  // 重复结构块的名称，使用{language}语言
            "description": str,  // 描述该结构块的特征和用途，包括：
                            // 1. 包含了什么类型的内容
                            // 2. 大致的重复次数
                            // 3. 子元素的主要特征
            "xpath": str,   // 定位到该结构块父容器的xpath
            "children_xpath": str,   // 定位到相似子元素项最里层的xpath
            "children_count": int  // 相似子元素项在页面中重复出现的次数
            "fields": [  // 该结构中包含的字段名称列表
                "字段1",
                "字段2"
            ]
        }}
    ]
}}
```
</output_format>
""",
    user="""请分析以下页面内容，优先识别所有字段，然后推导出逻辑结构：

{html_doc}
""",
)

field_first_prompt_xpath1 = Prompt(
    system="""
<task>
作为网页数据提取专家，你需要：
1. 扫描并提取HTML中所有有价值的数据字段
2. 分析这些字段之间的关系，识别它们是否属于相似的重复结构
3. 输出字段信息及其所属的结构区域组，请使用{language}语言回答
</task>

<analysis_process>
第一步：字段识别和提取
1. 扫描重要字段：
   - 商品/文章标题
   - 价格/时间/数量等数值型信息
   - 链接/图片等资源型信息
   - 状态/标签等分类信息
   
2. 字段定位方式：
   - 优先使用class等稳定属性定位
   - 确保xpath路径可靠且唯一
   - 记录字段的父容器xpath，用于后续结构分析

第二步：结构关系分析
1. 字段关联性判断：
   - 检查字段是否共享相同的父容器
   - 分析字段在DOM树中的相对位置
   - 判断字段组合是否构成完整信息单元

2. 重复结构识别：
   - 寻找具有相同字段组合的区域组
   - 确认这些区域组是否遵循相同的HTML模式
   - 提取这些重复区域的共同父容器
</analysis_process>

<xpath_best_practices>
1. 属性选择最佳实践：
   - 优先选择语义化class名称（如"price"、"title"、"description"等）
   - 避免使用生成的随机class名（如"a4f3e2"等）
   - 当使用多个属性条件时，从最具区分性的属性开始
   - 确保属性值完全匹配源码中的实际值，不要自行修改或简化
   - 避免使用文本内容匹配

2. 路径构建最佳实践：
   - 使用最少必要的层级来定位元素
   - 避免过长的xpath路径，减少因DOM结构变化导致的失效风险
   - 对于重复结构，先定位父容器，再定位子元素模式
   - 确保路径能覆盖所有相似元素，不仅仅是第一个
   - 注意：不要用`and`语法来叠加多个`contains`条件，以免造成匹配失败

3. 健壮性考虑：
   - 考虑页面动态变化，避免过于严格的条件
   - 对于可能的变化部分，使用更宽松的匹配策略
   - 测试xpath在不同状态下的有效性
   - 避免依赖特定的顺序或位置，除非确实必要
</xpath_best_practices>

<rules>
1. 字段识别规则：
   - 确保字段粒度适中（不过细、不过粗）
   - 字段必须可独立定位
   - 记录字段业务重要性
   - 标注字段值类型

2. 结构推导规则：
   - 分析字段DOM位置关系
   - 考虑字段业务关联性
   - 避免过度推导
   - 保持结构简洁清晰

3. xpath定位规则：
   - 使用相对路径，避免使用"."作为路径前缀，确保即使页面只展示部分内容也能正确匹配。
   - 基于属性匹配，确保属性值是正确的，始终使用contains()函数匹配完整的类名，严禁采用"="精确匹配。
   - 建议使用`[contains(@class, '类名')]`表达式,以防止因类名交叉问题而误判。
   - 不要使用`and`来叠加多个`contains`条件，以免造成匹配失败。
   - 根据页面实际情况，选择最稳定且具唯一性的属性进行匹配。如果class属性无法唯一标识目标，可以结合其他属性进行匹配。
   - 严禁使用文本内容（例如contains(text(), ...））定位元素，确保定位方式纯粹依赖属性和节点结构。
   - 尽量避免使用索引定位；如确需使用，请确保索引与页面结构的稳定性及正确性。
   - 全部使用"//"作为唯一的层级分隔符,以防路径过于固定。
   - 针对列表或表格等结构，需充分理解其布局（如表头与表体的关系），构造能够涵盖所有子项的公共父节点xpath。
   - 对于链接类字段，禁止依赖href属性，建议选择其他更稳定的属性进行定位。
   - 尽量避免依赖容易变动的属性（如`style`、`id`）。尽管`id`通常为唯一标识，但可能因页面更新而变化，建议优先使用较为稳定的`class`或其他属性来定位元素。
   - 对于数字字段，如果同时存在整数和小数部分，使用"|"符号组合多个xpath路径。
   - 对于包含多个class的元素，确保匹配完整的单个类名，使用`contains(@class, '完整类名')`方式。
</rules>

<common_errors_to_avoid>
1. 路径错误：
   - 路径分隔符没有使用双斜杠"//"
   - 使用"."作为路径起始
   - 路径层级关系与实际DOM结构不符
   - 使用了不存在的标签名或属性名

2. 属性匹配错误：
   - 使用"="而非contains()函数
   - 属性值不完整或不正确
   - 匹配了部分类名而非完整类名
   - 使用了不稳定或易变的属性

3. 结构理解错误：
   - 只匹配列表或表格的第一个元素
   - 未能识别重复结构的共同父容器
   - 错误地将不同结构的元素归为一组
   - 结构嵌套层级理解有误
</common_errors_to_avoid>

<output_format>
请按照如下JSON格式输出分析结果：

```json
{{
    "fields": [  // 提取的所有字段列表
        {{
            "field": "字段名称",  // 字段的名称, 使用{language}语言
            "xpath": "字段定位xpath",  // 用于准确定位该字段的xpath
            "value_type": "text/href/src/...",  // 字段值类型
        }}
    ],
    "similar_groups": [  // 识别出的重复结构区域项
        {{
            "name": "str",  // 重复结构块的名称，使用{language}语言
            "description": str,  // 描述该结构块的特征和用途，包括：
                            // 1. 包含了什么类型的内容
                            // 2. 大致的重复次数
                            // 3. 子元素的主要特征
            "xpath": str,   // 定位到该结构块父容器的xpath
            "children_xpath": str,   // 定位到相似子元素项的最外层xpath，需要注意使用class属性匹配
            "children_count": int  // 相似子元素项在页面中重复出现的次数
            "fields": [  // 该结构中包含的字段名称列表
                "字段1",
                "字段2"
            ]
        }}
    ]
}}
```
</output_format>
""",
    user="""请分析以下页面内容，优先识别所有字段，然后推导出逻辑结构：

{html_doc}
""",
)

specified_field_first_extract_prompt_xpath = Prompt(
    system="""
<task>
作为网页数据提取专家，你需要：
1. 扫描并提取HTML中的用户需要的数据字段
2. 分析这些字段之间的关系，识别它们是否属于相似的重复结构
3. 输出字段信息及其所属的结构区域组, 请使用{language}语言回答
</task>

<analysis_process>
第一步：字段识别和提取
1. 扫描重要字段：
   - 商品/文章标题
   - 价格/时间/数量等数值型信息
   - 链接/图片等资源型信息
   - 状态/标签等分类信息
   
2. 字段定位方式：
   - 优先使用class等稳定属性定位
   - 确保xpath路径可靠且唯一
   - 记录字段的父容器xpath，用于后续结构分析

第二步：结构关系分析
1. 字段关联性判断：
   - 检查字段是否共享相同的父容器
   - 分析字段在DOM树中的相对位置
   - 判断字段组合是否构成完整信息单元

2. 重复结构识别：
   - 寻找具有相同字段组合的区域组
   - 确认这些区域组是否遵循相同的HTML模式
   - 提取这些重复区域的共同父容器
</analysis_process>

<rules>
1. 字段识别规则：
   - 确保字段粒度适中（不过细/不过粗）
   - 字段必须可独立定位
   - 记录字段业务重要性
   - 标注字段值类型

2. 结构推导规则：
   - 分析字段DOM位置关系
   - 考虑字段业务关联性
   - 避免过度推导
   - 保持结构简洁清晰

3. xpath定位规则：
   - 使用相对路径，避免使用“.”作为路径前缀，确保即使页面只展示部分内容也能正确匹配。
   - 基于属性匹配，确保属性值是正确的，始终使用contains()函数匹配完整的类名，严禁采用"="精确匹配。
   - 建议使用`[contains(@class, '类名')]`表达式,以防止因类名交叉问题而误判。
   - 当一个条件已能唯一标识目标时，不必叠加过多`contains`条件，以免造成匹配失败。
   - 根据页面实际情况，选择最稳定且具唯一性的属性进行匹配。如果class属性无法唯一标识目标，可以结合其他属性进行匹配。
   - 严禁使用文本内容（例如contains(text(), ...））定位元素，确保定位方式纯粹依赖属性和节点结构。
   - 尽量避免使用索引定位；如确需使用，请确保索引与页面结构的稳定性及正确性。
   - 必须统一使用"//"作为层级分隔符，绝不使用单斜杠("/")以防路径过于固定。
   - 针对列表或表格等结构，需充分理解其布局（如表头与表体的关系），构造能够涵盖所有子项的公共父节点xpath。
   - 对于链接类字段，禁止依赖href属性，建议选择其他更稳定的属性进行定位。
   - 尽量避免依赖容易变动的属性（如`style`、`id`）。尽管`id`通常为唯一标识，但可能因页面更新而变化，建议优先使用较为稳定的`class`或其他属性来定位元素。
</rules>

<output_format>
请按照如下JSON格式输出分析结果：
```json
{{
    "fields": [  // 用户需要提取的字段列表
        {{
            "field": "字段名称",  // 字段的名称,使用{language}语言
            "xpath": "字段定位xpath",  // 用于准确定位该字段的xpath
            "value_type": "text/href/src/...",  // 字段值类型
        }}
    ],
    "similar_groups": [  // 识别出的重复结构区域项
        {{
            "name": "str",  // 重复结构块的名称，使用{language}语言
            "description": str,  // 描述该结构块的特征和用途，包括：
                            // 1. 包含了什么类型的内容
                            // 2. 大致的重复次数
                            // 3. 子元素的主要特征
            "xpath": str,   // 定位到该结构块父容器的xpath
            "children_xpath": str,   // 定位到相似子元素项的最外层xpath，需要注意使用class属性匹配
            "children_count": int  // 相似子元素项在页面中重复出现的次数
            "fields": [  // 该结构中包含的字段名称列表
                "字段1",
                "字段2"
            ]
        }}
    ]
}}
```
</output_format>
""",
    user="""需要分析下面关于{name}的html页面源码, 提取的字段包括：{fields}, 如果不存在相应字段, xpath可以为空。
可能的重复结构区域：{groups}

{html_doc}
""",
)


## css selector

field_extract_prompt_selector = Prompt(
    system="""你是一个优秀的网页分析专家，擅长分析网页结构和元素定位.
## task
用户给定一个网页的html, 识别页面中可以提取的字段并提取对应css selector

## references
你需要根据HTML结构选择合适的选择器:
1.使用CSS选择器:
  - CSS选择器有很多种类, 包括类型选择器、类选择器、ID选择器、属性选择器和伪类选择器等。
  - 根据目标元素的特征, 选择合适的选择器类型。例如, 如果元素有一个独特的类名, 您可以使用.类名作为选择器。
2.选择最合适的选择器类型:
  - 如果元素有唯一的ID, 使用ID选择器。
  - 如果元素属于某个特定的类, 使用类选择器。
  - 如果元素是唯一的标签类型, 考虑使用元素选择器。
  - 如果元素有特定的属性或属性值, 可以使用属性选择器。
3.检查元素的唯一性:
  - 如果目标元素在页面中只有一个, 那么您可以使用它的ID、特定的类名或标签名作为选择器。
  - 如果目标元素有多个, 您需要找到一种方法来区分它们。这可以是通过它们的顺序（使用:nth-child()或:nth-of-type()）、特定的属性或它们的父元素。
4.考虑元素的上下文:
  - 有时, 单独的元素选择器可能不够精确。在这种情况下, 您可以使用后代选择器（空格）、子元素选择器（>）或相邻兄弟选择器（+）来结合元素的父元素或兄弟元素。
记住, 选择器的选择应该尽可能精确和具体, 以避免选择到不需要的元素。
5.处理特殊情况:
  - 如果目标元素没有独特的标识符, 可能需要使用更复杂的组合选择器或伪类选择器。
注意处理具有相同类名或标签名的多个元素, 确保选择器能够区分它们。

## format
Your response should be ONLY a JSON with the following format:
```json
[
    {{
        "field": str,  // field name
        "selector": str  // the css selector of aimed field
    }}
]
```
""",
    user="""需要分析下面关于{name}的html页面

{html_doc}
""",
)

specified_field_extract_prompt_selector = Prompt(
    system="""你是一个优秀的网页分析专家，擅长分析网页结构和元素定位.
## task
用户给定一个网页的html, 识别页面中可以提取的字段并提取对应css selector

## references
你需要根据HTML结构选择合适的选择器:
1.使用CSS选择器:
  - CSS选择器有很多种类, 包括类型选择器、类选择器、ID选择器、属性选择器和伪类选择器等。
  - 根据目标元素的特征, 选择合适的选择器类型。例如, 如果元素有一个独特的类名, 您可以使用.类名作为选择器。
2.选择最合适的选择器类型:
  - 如果元素有唯一的ID, 使用ID选择器。
  - 如果元素属于某个特定的类, 使用类选择器。
  - 如果元素是唯一的标签类型, 考虑使用元素选择器。
  - 如果元素有特定的属性或属性值, 可以使用属性选择器。
3.检查元素的唯一性:
  - 如果目标元素在页面中只有一个, 那么您可以使用它的ID、特定的类名或标签名作为选择器。
  - 如果目标元素有多个, 您需要找到一种方法来区分它们。这可以是通过它们的顺序（使用:nth-child()或:nth-of-type()）、特定的属性或它们的父元素。
4.考虑元素的上下文:
  - 有时, 单独的元素选择器可能不够精确。在这种情况下, 您可以使用后代选择器（空格）、子元素选择器（>）或相邻兄弟选择器（+）来结合元素的父元素或兄弟元素。
记住, 选择器的选择应该尽可能精确和具体, 以避免选择到不需要的元素。
5.处理特殊情况:
  - 如果目标元素没有独特的标识符, 可能需要使用更复杂的组合选择器或伪类选择器。
注意处理具有相同类名或标签名的多个元素, 确保选择器能够区分它们。

## format
Your response should be ONLY a JSON with the following format:
```json
[
    {{
        "field": str,  // field name
        "selector": str  // the css selector of aimed field
    }}
]
```
""",
    user="""需要分析下面关于{name}的html页面源码, 提取的字段包括：{fields}, 如果不存在相应字段, xpath可以为空。

{html_doc}
""",
)


# pagination prompts
pagination_prompt_xpath = Prompt(
    system="""
<task>
你需要分析用户给定的一个网页的分页部分的html, 请分析并提取其中的字段和xpath。
</task>

<rules>
when generating xpath, please note:
- 不要使用href来定位元素，请使用属性class、id来定位。
- 使用属性class定位元素且属性匹配时请使用`contains`语法匹配部分属性。
- 不要不要匹配任何文本内容来定位元素。
- 使用索引定位元素时，请确保索引位置正确。
- 给定的页面可能是部分页面元素，总是用相对位置定位，不要用`.`定位。
- 请使用`//`语法替代`/`匹配层级关系。
- 属性名一定要在正确的标签元素中。
</rules>

<output_format>
Your response should ONLY be a JSON with the following format, `field` should be in English:
[
{{
    "field": str  // the field can be extract
    "xpath": str  // the xpath from source html, ensure the accuracy
}}
]
</output_format>
""",
    user="""需要分析下面分页部分的html页面源码, 提取(但不限于)的字段包括：[prev_page，next_page，current_page，total_pages，total_count，per_page_count]，如果不存在相应字段, xpath可以为空。

{html_doc}
""",
)


pagination_prompt_selector = Prompt(
    system="""
你需要分析用户给定的一个网页的分页部分的html, 请分析分页类型并提取其中的字段和css selector。

Your response should ONLY be a JSON with the following format, language is {language}:
[
{{
    "type": str    // the type of pagination, ajax or no-ajax
    "field": str  // the field can be extract
    "description": str  // description for the field
    "selector": str   // the css selector from source html, ensure the accuracy
}}
]
""",
    user="""需要分析下面分页器的html页面源码, 提取(但不限于)的字段包括：[上一页，下一页，当前页码，总页数，总条数，每页条数]，如果不存在相应字段, xpath可以为空。

{html_doc}
""",
)


# deprecated prompts

reflection_prompt_o1 = Prompt(
    system="""
你是一个优秀的网页分析专家，擅长分析网页结构和元素定位。你的任务是检查用户提供的HTML源码和错误的xpath结果，仔细分析并参考下面`xpath参考规则`修正xpath结果。请记住，我需要获取目标字段的对应所有匹配结果。

xpath参考规则:
- 检查xpath中的class属性是否在正确的元素名称内，确保它们都正确匹配。
- 检查xpath中的节点关系路径, 请使用语法`//`替换`/`。
- 如果使用了索引定位，请检查索引匹配是否正确。
- 使用class属性来定位内容。
- 使用`contains`语法来部分匹配class属性。
- 不要不要使用文本内容匹配，例如使用语法`contains(text(), "xxx")`来定位内容
- 如果遇到列表或表格结构，你需要理解表格结构，生成尽可能的匹配表格元素的xpath，而不是只匹配表头。

Your response should be ONLY a JSON with the following format:
```json
[
    {{
        "field": str,  // field name
        "thought": str,  // thought
        "xpath": str,  // give a new xpath, if field not exist, leave empty string.
        "value_type": str  // the value type can be extract from xpath, such as "text", "href", "src" etc.
    }}
]
```
""",
    user="""
错误的xpath结果： <error_result>{result}</error_result>


HTML源码: {html_doc}
""",
)


# reflection prompts

reflection_prompt_xpath_en = Prompt(
    system="""
<task>
You're a perfect discriminator which is good at HTML understanding as well. There are a xpath result extracted from HTML doc given by user, which can not match any elements. Please check step by step carefully and correct the xpath.
</task>

<rules>
checking steps:
1. Check the class attribute in xpath is inside the right element name, ensure they are all correct and matched.
2. Check the node relation path in xpath.
3. Always using "//" instead of "/" .
4. Using class attribute to locate content. 
5. Using `contains` to match class attribute.
6. If xpath use index to match element, double check the index is correct.
7. DO NOT USE text content match method in xpath.
</rules>

<output_format>
Your response should be only a JSON with the following format:
```json
[
    {{
        "field": str,  // field name
        "thought": str,  // thought of checking the xpath within 30 words,compact the new line, please check step by step.
        "xpath": str,  // give a new xpath, if field not exist, leave empty string.
        "value_type": str  // the value type can be extract from xpath, such as "text", "href", "src" etc.
    }}
]
```
</output_format>
""",
    user="""
请结合网页源码和错误的xpath结果(error_xpath)，分析并修正xpath结果,请记住我需要获取目标字段的所有匹配结果：

<error_xpath>
{result}
</error_xpath>


HTML doc: {html_doc}

""",
)

reflection_prompt_xpath = Prompt(
    system="""<task>
你是一个优秀的网页分析专家，擅长分析网页结构和元素定位。你的任务是:
1. 分析用户提供的网页源码和错误的xpath结果
2. 根据下述规则系统地检查并修正xpath
3. 确保修正后的xpath能准确匹配到目标字段的所有实例，请使用{language}语言回答
</task>

<xpath_check_sop>
1. 检查xpath语法基础
   - 不要使用"."来定位
   - 使用"//"替代"/"绝对路径
   - 检查方括号[]是否配对
   - 检查引号是否配对

2. 检查标签和属性
   - 确认HTML标签名称是否正确（如div、span、a等）
   - 验证属性是否属于对应标签
   - 将@class="xxx"改写为contains(@class, 'xxx')
   - 删除不稳定属性（如style、动态id）

3. 检查层级关系
   - 确认父子节点关系是否正确
   - 删除不必要的中间层级
   - 避免使用位置索引[1]
   - 确保重复项xpath能匹配到所有实例

4. 验证xpath结果
   - 测试xpath是否能匹配到目标元素
   - 检查是否存在误匹配
   - 确认是否覆盖所有相同结构的元素

5. 优化建议
   - 使用最具特征性的class属性
   - 路径尽可能简短
   - 避免使用text()函数
   - 优先使用class属性，其次考虑稳定的id属性、data-*属性
</xpath_check_sop>

<output_format>
Your response should be ONLY a JSON with the following format:
```json
[
    {{
        "field": str,  // field name，use {language}, keep the name as same as the actual content in web page
        "thinking": str,  // thinking step of xpath checking in one line, please check step by step.
        "xpath": str,  // xpath after correction, if field not exist, leave empty string.
        "value_type": str  // the value type can be extract from xpath, such as "text", "href", "src" etc.
    }}
]
```
</output_format>
""",
    user="""请分析以下网页源码中的错误xpath结果，并按照规则进行修正：

错误的xpath结果： 
<error_result>{result}</error_result>


HTML源码: 
{html_doc}
""",
)


reflection_prompt_xpath_area = Prompt(
    system="""<task>
你是一个优秀的网页分析专家，擅长分析网页结构和元素定位。你的任务是:
1. 分析用户提供的网页源码和错误的xpath结果,包含两个xpath、重复项区域xpath 和重复项子元素的xpath
2. 根据下述规则系统地检查并修正xpath
3. 确保修正后的xpath能准确匹配到目标字段的所有实例
4. 请使用{language}语言回答
</task>

<xpath_check_sop>
1. 检查xpath语法基础
   - 不要使用"."来定位
   - 使用"//"替代"/"绝对路径
   - 检查方括号[]是否配对
   - 检查引号是否配对

2. 检查标签和属性
   - 确认HTML标签名称是否正确（如div、span、a等）
   - 验证属性是否属于对应标签
   - 将@class="xxx"改写为contains(@class, 'xxx')
   - 删除不稳定属性（如style、动态id）

3. 检查层级关系
   - 确认父子节点关系是否正确
   - 删除不必要的中间层级
   - 避免使用位置索引[1]
   - 确保重复项xpath能匹配到所有实例

4. 验证xpath结果
   - 测试xpath是否能匹配到目标元素
   - 检查是否存在误匹配
   - 确认是否覆盖所有相同结构的元素

5. 优化建议
   - 使用最具特征性，具有语义化的class属性
   - 路径尽可能简短
   - 避免使用text()函数
   - 优先使用class属性，其次考虑稳定的的id属性、data-*属性
</xpath_check_sop>

<output_format>
Your response should be ONLY a JSON with the following format:
```json
[
    {{
        "name": str,  // structure name, use {language}, keep the name as same as the actual content in web page
        "thinking": str,  // thinking of checking `xpath` and `children_xpath` in one line，please check step by step.
        "xpath": str,  // xpath after correction
        "children_xpath": str  // children xpath after correction
    }}
]
```
</output_format>
""",
    user="""请分析以下网页源码中的错误xpath结果，并按照规则进行修正：

错误的xpath结果： 
<error_result>{result}</error_result>


HTML源码: 
{html_doc}
""",
)


## css selector
reflection_prompt_selector = Prompt(
    system="""
You're a perfect discriminator which is good at HTML understanding as well. There are a css selector result extracted from HTML doc given by user, which can not match any elements. Please check step by step carefully and correct the xpath.
when checking :
1.使用CSS选择器:
  - CSS选择器有很多种类, 包括类型选择器、类选择器、ID选择器、属性选择器和伪类选择器等。
  - 根据目标元素的特征, 选择合适的选择器类型。例如, 如果元素有一个独特的类名, 您可以使用.类名作为选择器。
2.选择最合适的选择器类型:
  - 如果元素有唯一的ID, 使用ID选择器。
  - 如果元素属于某个特定的类, 使用类选择器。
  - 如果元素是唯一的标签类型, 考虑使用元素选择器。
  - 如果元素有特定的属性或属性值, 可以使用属性选择器。
3.检查元素的唯一性:
  - 如果目标元素在页面中只有一个, 那么您可以使用它的ID、特定的类名或标签名作为选择器。
  - 如果目标元素有多个, 您需要找到一种方法来区分它们。这可以是通过它们的顺序（使用:nth-child()或:nth-of-type()）、特定的属性或它们的父元素。
4.考虑元素的上下文:
  - 有时, 单独的元素选择器可能不够精确。在这种情况下, 您可以使用后代选择器（空格）、子元素选择器（>）或相邻兄弟选择器（+）来结合元素的父元素或兄弟元素。
记住, 选择器的选择应该尽可能精确和具体, 以避免选择到不需要的元素。
5.处理特殊情况:
  - 如果目标元素没有独特的标识符, 可能需要使用更复杂的组合选择器或伪类选择器。
注意处理具有相同类名或标签名的多个元素, 确保选择器能够区分它们。


Your response should be only a JSON with the following format:
```json
[
    {{
        "field": str,  // field name
        "thought": str,  // thought of your checking within 20 words, please check step by step.
        "selector": str  // give a new css selector, if field not exist, leave empty string.
    }}
]
```
""",
    user="""
请结合网页源码和错误的css selector结果，分析并修正css selector结果,请记住我需要获取目标字段的所有匹配结果：

错误的selector结果： {result}


HTML doc: {html_doc}

""",
)

# dom reducer prompts
dom_reducer_prompt_xpath = Prompt(
    system="""<task>
你是一位资深网页结构分析专家。你的任务是分析网页中的重复性结构块(blocks)，这些blocks通常包含多个结构相似的子元素。请识别这些blocks并提供其xpath路径。
</task>

<rules>
1. 识别相似元素组的特征：
   - 具有相同或相似的HTML结构模式
   - 共享同一个父元素容器
   - 使用相同或规律性的类名
   - 内部子元素结构保持一致

2. 常见的相似元素组类型(包含但不限于以下类型)：
   - 商品列表中的商品卡片
   - 评论列表中的评论项
   - 新闻列表中的新闻条目
   - 表格中的数据行
   - 导航菜单中的菜单项
   - 分页器中的页码按钮

3. xpath生成规则：
   - 必须定位到包含所有相似子元素的父级容器
   - 使用contains()函数进行属性匹配，确保匹配完整的类名
   - 统一使用//作为层级分隔符
   - 不使用.作为定位符
   - 避免使用位置索引，除非确实必要
   - 不同blocks之间不能有重叠或嵌套关系

4. 分析优先级：
   - 优先识别页面主要内容区域的重复结构
   - 其次关注功能性区域的重复结构
   - 最后考虑装饰性或次要区域的重复结构
</rules>

<output_format>
请按照以下JSON格式输出结果：
```json
[
    {{
        "block": str,  // 重复结构块的名称，使用中文
        "description": str,  // 描述该结构块的特征和用途，包括：
                            // 1. 包含了什么类型的内容
                            // 2. 大致的重复次数
                            // 3. 子元素的主要特征
        "xpath": str,   // 定位到该结构块父容器的xpath
        "children_xpath": str,   // 定位到相似子元素项的最外层xpath，需要注意使用class属性匹配
        "children_count": int  // 相似子元素项在页面中重复出现的次数
    }}
]
```
</output_format>
""",
    user="""请分析下面页面中的重复性结构块：

{html_doc}
""",
)

dom_reducer_prompt_xpath1 = Prompt(
    system="""<task>
用户给定一个网页的html, 你需要进一步精简网页, 请分析网页中重复且占较多的blocks(可以有多个，但是block之间不能有交集), 并返回对应xpath。
</task>

<rules>
when generating xpath:
- 列表或表格型子部分页面(产品或商品的列表)通常由许多相似子item组成, 请获取相似子元素（外层包含所有子item）的xpath。
- 属性匹配时请使用`contains`语法。
- 请使用`//`语法匹配层级关系。
</rules>


<output_format>
Your response should ONLY be a JSON with the following format:
```json
[
{{
    "block": str  // repeated block , using Chinese
    "description": str  // description for the block, using Chinese
    "xpath": str   // the xpath from source html, ensure the accuracy
}}
]
```
</output_format>
""",
    user="""需要分析下面页面:

{html_doc}
""",
)
