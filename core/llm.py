import json
from dataclasses import dataclass

import requests
from loguru import logger

from core.utils.cleanup_html import cleanup_html

from .utils.get_elements import get_elements
from .utils.token_calculator import truncate_text_tokens

api_base = "https://llm-pool.nlp.yuntingai.com"
api_key = "sk-zNVkUNmkZWyOkrq7JkVPCQdc8H"


@dataclass
class Prompt:
    system: str
    user: str


# NOTE 使用 llama-3-instruct-latest 模型时, system 中需要使用双花括号 {{ }}
# 这是由于训练时候的数据使用了双括号所致
prompt = Prompt(
    system="""
你需要分析用户给定的一个网页源码, 并返回其中特定类型的 xpath 路径.

Your response should be a JSON with the following format:
[
{
    "type": str  // the type of the content
    "xpath": str  // the xpath of the content
}
]
""",
    user="""
需要分析并返回的 xpath 路径类型 (type) 包括:
{fields}

{html_doc}
""",
)


def prepare_html_docs(html_doc: str, max_tokens: int):
    # 清洗
    logger.info(f"original {len(html_doc)=}")
    _, html_doc, _, _ = cleanup_html(html_doc, base_url="dummy_base_url")
    logger.info(f"cleaned {len(html_doc)=}")
    # 裁剪
    html_docs = truncate_text_tokens(html_doc, max_tokens, encoding_name="cl100k_base")
    return html_docs


def find_xpath(html_doc: str, fields: list[str], verbose: bool = False):
    """判断给定文本的类别"""

    # 请求大模型进行分析
    resp = requests.post(
        f"{api_base}/chat/completions",
        headers={"Authorization": f"Bearer {api_key}"},
        json={
            "messages": [
                {
                    "role": "system",
                    "content": prompt.system,
                },
                {
                    "role": "user",
                    "content": prompt.user.format(
                        fields="\n".join([f"- {field}" for field in fields]),
                        html_doc=html_doc,
                    ),
                },
            ],
            # "model": "claude-3-haiku-20240307",
            "model": "gpt-4o-mini",
            "temperature": 0.1,
            "max_tokens": 4096,
            "stream": False,
        },
    ).json()

    # 大模型结果解析
    try:
        resp = resp["choices"][0]["message"]["content"]
        resp = resp.replace("```json", "").replace("```", "")
        results = json.loads(resp)
    except Exception as e:
        print(resp)
        raise e

    # 验证 xpath 有效性
    if verbose:
        for item in results:
            field = item["type"]
            xpath = item["xpath"]
            elements = get_elements(xpath, html_doc)
            print(f"[{field}] {xpath}")
            for element in elements:
                print(element)
            print()

    return results
