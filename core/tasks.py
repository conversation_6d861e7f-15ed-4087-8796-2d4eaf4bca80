"""
CrewAI Task定义模块
定义各种分析任务，保留原有提示词逻辑
"""

from crewai import Task
from typing import Dict, List, Any, Optional
from pydantic import BaseModel


class TaskInput(BaseModel):
    """任务输入数据模型"""
    html_content: str
    mode: str = "xpath"
    language: str = "zh_CN"
    fields: Optional[List[str]] = None
    groups: Optional[List[str]] = None
    name: Optional[str] = ""
    enable_refine: bool = True


def create_html_structure_analysis_task(
    agent, 
    html_content: str, 
    mode: str = "xpath", 
    language: str = "zh_CN",
    fields: Optional[List[str]] = None
) -> Task:
    """创建HTML结构分析任务"""
    
    description = f"""
    分析提供的HTML网页结构，识别页面中的主要语义区域。
    
    任务要求：
    1. 仔细分析HTML文档的整体布局结构
    2. 识别各种语义区域：导航栏、搜索框、内容区、侧边栏、页脚、广告区、翻页控件等
    3. 为每个识别的区域生成准确的{'XPath' if mode == 'xpath' else 'CSS选择器'}路径
    4. 特别关注翻页控件的识别，判断分页模式类型
    5. 使用{language}语言进行描述
    
    HTML内容：
    {html_content[:2000]}...
    
    定位模式：{mode}
    语言设置：{language}
    {"指定字段：" + str(fields) if fields else ""}
    """
    
    expected_output = """
    返回JSON格式的分析结果，包含识别的语义区域列表：
    [
        {
            "type": "区域类型名称",
            "xpath": "XPath路径" (如果mode为xpath),
            "selector": "CSS选择器" (如果mode为selector),
            "description": "区域描述"
        }
    ]
    """
    
    return Task(
        description=description,
        expected_output=expected_output,
        agent=agent,
        tools=[agent.tools[0]] if agent.tools else [],
    )


def create_field_extraction_task(
    agent,
    html_content: str,
    fields: List[str],
    mode: str = "xpath",
    language: str = "zh_CN",
    name: str = "",
    groups: Optional[List[str]] = None
) -> Task:
    """创建字段提取任务"""
    
    description = f"""
    从HTML页面中提取指定的数据字段，生成准确的定位路径。
    
    任务要求：
    1. 分析HTML结构，理解页面内容和布局
    2. 根据字段名称的语义含义，在HTML中定位对应的元素
    3. 为每个字段生成稳定可靠的{'XPath' if mode == 'xpath' else 'CSS选择器'}路径
    4. 确保生成的路径能够准确匹配目标元素
    5. 对于列表型数据，提取包含所有子项的父级路径
    6. 使用{language}语言进行描述
    
    页面名称：{name}
    目标字段：{fields}
    {"分组信息：" + str(groups) if groups else ""}
    HTML内容：
    {html_content[:2000]}...
    
    定位模式：{mode}
    语言设置：{language}
    """
    
    if groups:
        expected_output = """
        返回JSON格式的结果，包含字段信息和相似元素组：
        {
            "fields": [
                {
                    "field": "字段名称",
                    "xpath": "XPath路径" (如果mode为xpath),
                    "selector": "CSS选择器" (如果mode为selector),
                    "description": "字段描述"
                }
            ],
            "similar_groups": [
                {
                    "name": "组名称",
                    "description": "组描述",
                    "xpath": "容器XPath" (如果mode为xpath),
                    "children_xpath": "子元素XPath" (如果mode为xpath),
                    "container_selector": "容器选择器" (如果mode为selector),
                    "children_selector": "子元素选择器" (如果mode为selector)
                }
            ]
        }
        """
    else:
        expected_output = """
        返回JSON格式的字段提取结果：
        [
            {
                "field": "字段名称",
                "xpath": "XPath路径" (如果mode为xpath),
                "selector": "CSS选择器" (如果mode为selector),
                "description": "字段描述"
            }
        ]
        """
    
    return Task(
        description=description,
        expected_output=expected_output,
        agent=agent,
        tools=[agent.tools[0]] if agent.tools else [],
    )


def create_pagination_analysis_task(
    agent,
    html_content: str,
    mode: str = "xpath",
    language: str = "zh_CN"
) -> Task:
    """创建分页控件分析任务"""
    
    description = f"""
    分析HTML中的分页控件，识别各种分页机制和相关元素。
    
    任务要求：
    1. 仔细检查HTML中的分页相关元素
    2. 识别分页控件类型：传统分页(Bar)、加载更多(LoadMore)、无限滚动(InfiniteScroll)、虚拟滚动(VirtualScroll)
    3. 提取分页相关的关键元素：上一页、下一页、页码、加载更多按钮等
    4. 为每个分页元素生成准确的{'XPath' if mode == 'xpath' else 'CSS选择器'}路径
    5. 使用{language}语言进行描述
    
    HTML内容：
    {html_content[:1500]}...
    
    定位模式：{mode}
    语言设置：{language}
    """
    
    expected_output = """
    返回JSON格式的分页分析结果：
    [
        {
            "type": "分页元素类型",
            "xpath": "XPath路径" (如果mode为xpath),
            "selector": "CSS选择器" (如果mode为selector),
            "description": "元素描述"
        }
    ]
    """
    
    return Task(
        description=description,
        expected_output=expected_output,
        agent=agent,
        tools=[agent.tools[0]] if agent.tools else [],
    )


def create_html_optimization_task(
    agent,
    html_content: str,
    operation: str = "reduce",
    mode: str = "xpath",
    language: str = "zh_CN"
) -> Task:
    """创建HTML优化任务"""
    
    if operation == "reduce":
        description = f"""
        优化和精简HTML内容，移除冗余元素，保留关键数据结构。
        
        任务要求：
        1. 分析HTML结构，识别对数据提取无用的元素
        2. 移除广告、脚本、样式等冗余内容
        3. 保留重要的数据结构和语义信息
        4. 减少HTML的复杂度和token消耗
        5. 记录被移除的元素信息
        
        HTML内容：
        {html_content[:2000]}...
        
        语言设置：{language}
        """
        
        expected_output = """
        返回JSON格式的优化结果：
        {
            "truncate_html": "精简后的HTML内容",
            "similar_group": [
                {
                    "xpath": "被移除元素的XPath",
                    "description": "移除原因"
                }
            ]
        }
        """
    
    else:  # find_similar
        description = f"""
        识别HTML中的相似元素组，找出重复的结构模式。
        
        任务要求：
        1. 扫描HTML结构，寻找重复出现的元素模式
        2. 识别列表项、卡片、表格行等相似结构
        3. 分析这些相似元素的共同特征
        4. 为相似元素组生成统一的定位路径
        5. 统计相似元素的数量
        
        HTML内容：
        {html_content[:2000]}...
        
        语言设置：{language}
        """
        
        expected_output = """
        返回JSON格式的相似元素组分析结果：
        {
            "similar_group": [
                {
                    "name": "相似元素组名称",
                    "xpath": "元素组XPath",
                    "count": "元素数量",
                    "description": "元素组描述"
                }
            ]
        }
        """
    
    return Task(
        description=description,
        expected_output=expected_output,
        agent=agent,
        tools=[agent.tools[0]] if agent.tools else [],
    )


def create_validation_task(
    agent,
    html_content: str,
    results: List[Dict],
    mode: str = "xpath",
    language: str = "zh_CN",
    result_type: str = "fields"
) -> Task:
    """创建验证和修正任务"""
    
    description = f"""
    验证和修正{'XPath' if mode == 'xpath' else 'CSS选择器'}的准确性，确保数据提取的可靠性。
    
    任务要求：
    1. 逐一验证提供的{'XPath' if mode == 'xpath' else 'CSS选择器'}路径
    2. 检查路径是否能在HTML中找到对应元素
    3. 识别无效或不准确的路径
    4. 对错误的路径进行修正和优化
    5. 确保修正后的路径稳定可靠
    6. 使用{language}语言进行描述
    
    待验证结果：
    {str(results)[:1000]}...
    
    HTML内容：
    {html_content[:1500]}...
    
    定位模式：{mode}
    结果类型：{result_type}
    语言设置：{language}
    """
    
    expected_output = """
    返回验证和修正后的结果，保持原有格式不变，但确保所有路径都是有效的。
    对于无效的路径，提供修正后的准确路径。
    """
    
    return Task(
        description=description,
        expected_output=expected_output,
        agent=agent,
        tools=[agent.tools[0]] if agent.tools else [],
    )


def create_chat_coordination_task(
    agent,
    user_query: str,
    context: Dict[str, Any],
    language: str = "zh_CN"
) -> Task:
    """创建对话协调任务"""
    
    description = f"""
    作为智能对话助手，理解用户需求并协调其他专家提供最佳的数据采集方案。
    
    任务要求：
    1. 理解用户的查询意图和需求
    2. 分析当前的上下文信息
    3. 确定需要调用哪些专家来解决问题
    4. 协调各个专家的工作流程
    5. 为用户提供清晰的指导和建议
    6. 使用{language}语言进行回复
    
    用户查询：{user_query}
    
    上下文信息：
    {str(context)[:1000]}...
    
    语言设置：{language}
    """
    
    expected_output = """
    返回JSON格式的协调结果：
    {
        "analysis": "需求分析",
        "suggestion": "建议方案",
        "steps": ["具体步骤1", "具体步骤2", "..."],
        "next_actions": ["后续行动1", "后续行动2", "..."]
    }
    """
    
    return Task(
        description=description,
        expected_output=expected_output,
        agent=agent,
        allow_delegation=True,
    )
