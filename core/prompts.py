from dataclasses import dataclass

@dataclass
class Prompt:
    system: str
    user: str


# NOTE 使用 llama-3-instruct-latest 模型时, system 中需要使用双花括号 {{ }}
# 这是由于训练时候的数据使用了双括号所致
prompt = Prompt(
    system="""
你是一个网页元素提取器，你需要分析用户给定的一个网页源码, 并返回其中特定类型的 xpath 路径.

Your response should be a JSON with the following format:
[
{{
    "type": str  // the type of the field
    "xpath": str  // the xpath of aimed field, keep it concise and accuracy
}}
]
""",
    user="""
需要分析并返回网页源码中的 xpath 路径类型 (type) 包括:
{fields}
when generating xpath:
- 数字类型可能会有整数和小数两个部分的标签，可以用 `|` 表示并列路径。
- 生成xpath的时候请使用contain属性匹配语法


{html_doc}
""",
)


css_selector_prompt = Prompt(
    system="""
你是一个网页元素提取器，你需要分析用户给定的一个网页源码, 并返回其中特定类型的css selector 路径.

Your response should be a JSON with the following format:
[
{{
    "type": str  // the type of the field
    "selector": str  // the css selector of aimed field, keep it concise and accuracy
}}
]
""",
    user="""
需要分析并返回网页源码中的 css selector 类型 (type) 包括:
{fields}
when generating css selector:
- 数字类型可能会有整数和小数两个部分的标签,需要表示并列路径。


{html_doc}
""",
)


direct_extract_prompt = Prompt(
    system="""你是一个网页元素提取器，你需要分析用户给定的一个网页源码, 并返回指定类型的内容字段.

Your response should be ONLY a JSON with the following format:
```json
[
{{
    "field_name": value  // the key field_name should be the one of user defined fields, and the value should be the corresponding value.
    ...
}}
]
```
""",
    user="""需要分析并按顺序返回全部符合要求的元素(排除广告和推荐内容)，字段包括:
{fields},字段值可以为空

{html_doc}"""
)


html_structure_prompt = Prompt(
    system="""
你需要分析用户给定的一个网页源码, 识别页面的各个组成子部分(例如导航栏、侧边栏、广告栏、翻页栏(可能是滚动加载)等等)并提取对应xpath

Your response should ONLY be a JSON with the following format:
[
    {{
        "type": str, // the type of the sub-part content, using Chinese
        "reason": str,  // reason why extract the sub-part, using Chinese
        "xpath": str   // the xpath from source html, ensure the accuracy
    }}
]
""",
    user="""需要分析下面页面:

{html_doc}
"""
)

field_extract_prompt = Prompt(
    system="""
你需要分析用户给定的一个网页源码, 识别页面中可以提取的字段名并提取对应xpath

Your response should ONLY be a JSON with the following format:
[
{{
    "field": str  // the field can be extract, using Chinese
    "reason": str  // reason why extract the field, using Chinese
    "xpath": str   // the xpath from source html, ensure the accuracy
}}
]
""",
    user="""需要分析下面关于{name}的html页面:

{html_doc}
"""
)

field_selector_extract_prompt = Prompt(
    system="""
你需要分析用户给定的一个网页源码, 识别页面的可能可以提取的字段并提取对应css selector

Your response should ONLY be a JSON with the following format:
[
{{
    "field": str  // the field can be extract, using Chinese
    "reason": str  // reason why extract the field, using Chinese
    "selector": str   // the css selector from source html, ensure the accuracy
}}
]
""",
    user="""需要分析下面页面:

{html_doc}
"""
)

specified_field_extract_prompt = Prompt(
    system="""
你需要分析用户给定的一个网页源码, 提取对应字段信息和xpath。

Your response should ONLY be a JSON with the following format:
[
{{
    "field": str  // the field can be extract, using Chinese
    "reason": str  // reason why extract the field, using Chinese
    "xpath": str   // the xpath from source html, ensure the accuracy
}}
]
""",
    user="""需要分析下面关于{name}的html页面源码, 提取的字段包括：{fields}。

{html_doc}
"""
)



multi_xpath_prompt = Prompt(
    system="""
你是一个网页元素提取器，你需要分析用户给定的一个网页源码, 并返回其中特定类型的 xpath 路径.

Your response should be a JSON with the following format:
[
{{
    "type": str  // the type of the field
    "xpath": list[str]  // three possible xpath of aimed field, keep it concise and accuracy
}}
]
""",
    user="""
需要分析并返回网页源码中的 xpath 路径类型 (type) 包括:
{fields}
when generating xpath:
- 数字类型可能会有整数和小数两个部分的标签，可以用 `|` 表示并列路径。
- 生成xpath的时候请使用contain属性匹配语法


{html_doc}
""",
)




reflection_prompt = Prompt(
    system="""
You're a perfect discriminator which is good at HTML understanding as well. There are a xpath result extracted from HTML doc given by user, please check carefully and generate an new xpath：
1. decompose the xpath node by node.
2. check each class attribute is exists in the right node element.
3. using contains not equel(=).

Demonstration:
xpath：//e1[contains(@class, 'var')]/e2
thought：
1. check if class attribute var is in element e1? NO, it's in element e3.
2. check if element e2 is the children or subchildren of previous element? Yes, it is a child element under element e3.


Your response should be a JSON with the follo format:
[
    {{
        "thought": str,  // thought of checking the xpath 
        "xpath": str  // give a new xpath
    }}
]
""",
    user="""
instruction：需要分析并返回网页源码中的 xpath 路径类型 (type) 包括:{fields}

xpath： {result}


HTML doc: {html_doc}

"""
)