"""
CrewAI Crew协调模块
定义Agent协作流程和任务分配
"""

from crewai import Crew, Process
from typing import Dict, List, Any, Optional
import json
import time

from .agents import (
    create_html_structure_analyst,
    create_field_extraction_expert,
    create_pagination_analyst,
    create_html_optimizer,
    create_quality_validator,
    create_chat_assistant
)

from .tasks import (
    create_html_structure_analysis_task,
    create_field_extraction_task,
    create_pagination_analysis_task,
    create_html_optimization_task,
    create_validation_task,
    create_chat_coordination_task
)


class Chat4DataCrew:
    """Chat4Data多Agent协作系统"""
    
    def __init__(self):
        """初始化所有Agent"""
        self.html_structure_analyst = create_html_structure_analyst()
        self.field_extraction_expert = create_field_extraction_expert()
        self.pagination_analyst = create_pagination_analyst()
        self.html_optimizer = create_html_optimizer()
        self.quality_validator = create_quality_validator()
        self.chat_assistant = create_chat_assistant()
        
        # 创建不同功能的Crew
        self._setup_crews()
    
    def _setup_crews(self):
        """设置不同功能的Crew"""
        
        # HTML结构分析Crew
        self.structure_crew = Crew(
            agents=[self.html_structure_analyst, self.quality_validator],
            tasks=[],  # 任务将在运行时动态创建
            process=Process.sequential,
            verbose=True,
        )
        
        # 字段提取Crew
        self.extraction_crew = Crew(
            agents=[self.field_extraction_expert, self.quality_validator],
            tasks=[],
            process=Process.sequential,
            verbose=True,
        )
        
        # 分页分析Crew
        self.pagination_crew = Crew(
            agents=[self.pagination_analyst, self.quality_validator],
            tasks=[],
            process=Process.sequential,
            verbose=True,
        )
        
        # HTML优化Crew
        self.optimization_crew = Crew(
            agents=[self.html_optimizer],
            tasks=[],
            process=Process.sequential,
            verbose=True,
        )
        
        # 对话协调Crew
        self.chat_crew = Crew(
            agents=[self.chat_assistant],
            tasks=[],
            process=Process.sequential,
            verbose=True,
        )
    
    def analyze_html_structure(
        self,
        html_content: str,
        mode: str = "xpath",
        language: str = "zh_CN",
        fields: Optional[List[str]] = None,
        enable_refine: bool = True
    ) -> Dict[str, Any]:
        """分析HTML结构"""
        
        # 创建结构分析任务
        analysis_task = create_html_structure_analysis_task(
            agent=self.html_structure_analyst,
            html_content=html_content,
            mode=mode,
            language=language,
            fields=fields
        )
        
        tasks = [analysis_task]
        
        # 如果启用修正，添加验证任务
        if enable_refine:
            validation_task = create_validation_task(
                agent=self.quality_validator,
                html_content=html_content,
                results=[],  # 将从前一个任务获取结果
                mode=mode,
                language=language,
                result_type="fields"
            )
            tasks.append(validation_task)
        
        # 更新Crew任务
        self.structure_crew.tasks = tasks
        
        # 执行任务
        try:
            result = self.structure_crew.kickoff()
            
            # 处理结果
            if isinstance(result, str):
                try:
                    parsed_result = json.loads(result)
                except json.JSONDecodeError:
                    parsed_result = result
            else:
                parsed_result = result
            
            return {
                "success": True,
                "data": parsed_result,
                "metadata": {
                    "agents_used": ["html_structure_analyst"] + (["quality_validator"] if enable_refine else []),
                    "execution_time": time.time()
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": []
            }
    
    def extract_fields(
        self,
        html_content: str,
        fields: List[str],
        mode: str = "xpath",
        language: str = "zh_CN",
        name: str = "",
        groups: Optional[List[str]] = None,
        enable_refine: bool = True
    ) -> Dict[str, Any]:
        """提取字段"""
        
        # 创建字段提取任务
        extraction_task = create_field_extraction_task(
            agent=self.field_extraction_expert,
            html_content=html_content,
            fields=fields,
            mode=mode,
            language=language,
            name=name,
            groups=groups
        )
        
        tasks = [extraction_task]
        
        # 如果启用修正，添加验证任务
        if enable_refine:
            validation_task = create_validation_task(
                agent=self.quality_validator,
                html_content=html_content,
                results=[],
                mode=mode,
                language=language,
                result_type="areas" if groups else "fields"
            )
            tasks.append(validation_task)
        
        # 更新Crew任务
        self.extraction_crew.tasks = tasks
        
        # 执行任务
        try:
            result = self.extraction_crew.kickoff()
            
            # 处理结果
            if isinstance(result, str):
                try:
                    parsed_result = json.loads(result)
                except json.JSONDecodeError:
                    parsed_result = result
            else:
                parsed_result = result
            
            return {
                "success": True,
                "data": parsed_result,
                "metadata": {
                    "agents_used": ["field_extraction_expert"] + (["quality_validator"] if enable_refine else []),
                    "execution_time": time.time()
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": []
            }
    
    def analyze_pagination(
        self,
        html_content: str,
        mode: str = "xpath",
        language: str = "zh_CN",
        enable_refine: bool = True
    ) -> Dict[str, Any]:
        """分析分页控件"""
        
        # 创建分页分析任务
        pagination_task = create_pagination_analysis_task(
            agent=self.pagination_analyst,
            html_content=html_content,
            mode=mode,
            language=language
        )
        
        tasks = [pagination_task]
        
        # 如果启用修正，添加验证任务
        if enable_refine:
            validation_task = create_validation_task(
                agent=self.quality_validator,
                html_content=html_content,
                results=[],
                mode=mode,
                language=language,
                result_type="fields"
            )
            tasks.append(validation_task)
        
        # 更新Crew任务
        self.pagination_crew.tasks = tasks
        
        # 执行任务
        try:
            result = self.pagination_crew.kickoff()
            
            # 处理结果
            if isinstance(result, str):
                try:
                    parsed_result = json.loads(result)
                except json.JSONDecodeError:
                    parsed_result = result
            else:
                parsed_result = result
            
            return {
                "success": True,
                "data": parsed_result,
                "metadata": {
                    "agents_used": ["pagination_analyst"] + (["quality_validator"] if enable_refine else []),
                    "execution_time": time.time()
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": []
            }
    
    def optimize_html(
        self,
        html_content: str,
        operation: str = "reduce",
        mode: str = "xpath",
        language: str = "zh_CN"
    ) -> Dict[str, Any]:
        """优化HTML"""
        
        # 创建HTML优化任务
        optimization_task = create_html_optimization_task(
            agent=self.html_optimizer,
            html_content=html_content,
            operation=operation,
            mode=mode,
            language=language
        )
        
        # 更新Crew任务
        self.optimization_crew.tasks = [optimization_task]
        
        # 执行任务
        try:
            result = self.optimization_crew.kickoff()
            
            # 处理结果
            if isinstance(result, str):
                try:
                    parsed_result = json.loads(result)
                except json.JSONDecodeError:
                    parsed_result = result
            else:
                parsed_result = result
            
            return {
                "success": True,
                "data": parsed_result,
                "metadata": {
                    "agents_used": ["html_optimizer"],
                    "execution_time": time.time()
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": {}
            }
    
    def chat_coordination(
        self,
        user_query: str,
        context: Dict[str, Any],
        language: str = "zh_CN"
    ) -> Dict[str, Any]:
        """对话协调"""
        
        # 创建对话协调任务
        chat_task = create_chat_coordination_task(
            agent=self.chat_assistant,
            user_query=user_query,
            context=context,
            language=language
        )
        
        # 更新Crew任务
        self.chat_crew.tasks = [chat_task]
        
        # 执行任务
        try:
            result = self.chat_crew.kickoff()
            
            # 处理结果
            if isinstance(result, str):
                try:
                    parsed_result = json.loads(result)
                except json.JSONDecodeError:
                    parsed_result = {"answer": result}
            else:
                parsed_result = result
            
            return {
                "success": True,
                "data": parsed_result,
                "metadata": {
                    "agents_used": ["chat_assistant"],
                    "execution_time": time.time()
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": {"answer": "抱歉，处理您的请求时出现了错误。"}
            }


# 全局Crew实例
_crew_instance = None

def get_crew_instance() -> Chat4DataCrew:
    """获取全局Crew实例（单例模式）"""
    global _crew_instance
    if _crew_instance is None:
        _crew_instance = Chat4DataCrew()
    return _crew_instance
