"""
CrewAI Agent定义模块
定义各个专业Agent的角色、目标、背景故事和工具
"""

from crewai import Agent
from crewai_tools import BaseTool
from typing import Dict, List, Any
import json
import os
from pydantic import BaseModel, Field

from core.llm_v2 import request_llm, message_formatter, CHAT_MODEL
from core.utils.get_elements import is_valid_xpath, is_valid_selector
from core.utils.token_calculator import truncate_text_tokens, truncate_html_by_xpath

# 配置CrewAI使用的LLM
os.environ["OPENAI_API_BASE"] = os.getenv("API_BASE", "https://litellm.nlp.yuntingai.com")
os.environ["OPENAI_API_KEY"] = os.getenv("API_KEY", "sk-0hyQ1IdbZ1DM_SEKraTeQQ")
os.environ["OPENAI_MODEL_NAME"] = os.getenv("CHAT_MODEL", "gemini-2.0-flash-001")


class HTMLAnalysisTool(BaseTool):
    """HTML分析工具基类"""
    name: str = "HTML Analysis Tool"
    description: str = "Base tool for HTML analysis"
    
    def _run(self, html_content: str, **kwargs) -> Dict[str, Any]:
        """执行HTML分析"""
        raise NotImplementedError


class StructureAnalysisTool(HTMLAnalysisTool):
    """网页结构分析工具"""
    name: str = "Structure Analysis Tool"
    description: str = "Analyze HTML structure and identify semantic regions like navigation, content areas, sidebars, etc."
    
    def _run(self, html_content: str, mode: str = "xpath", language: str = "zh_CN", fields: List[str] = None, **kwargs) -> Dict[str, Any]:
        """分析HTML结构"""
        from core.task_prompt import (
            html_structure_prompt_xpath,
            html_structure_prompt_selector,
            specified_html_structure_prompt_xpath,
            specified_html_structure_prompt_selector
        )
        
        system_kwargs = {"language": language}
        user_kwargs = {"html_doc": html_content}
        
        # 选择合适的提示词
        if fields:
            user_kwargs["fields"] = fields
            prompt = (
                specified_html_structure_prompt_xpath
                if mode == "xpath"
                else specified_html_structure_prompt_selector
            )
        else:
            prompt = (
                html_structure_prompt_xpath
                if mode == "xpath"
                else html_structure_prompt_selector
            )
        
        messages = message_formatter(prompt, system_kwargs, user_kwargs)
        result = request_llm(
            messages,
            model=CHAT_MODEL,
            verbose=True,
            parse_json=True,
        )
        
        return result["content"]


class FieldExtractionTool(HTMLAnalysisTool):
    """字段提取工具"""
    name: str = "Field Extraction Tool"
    description: str = "Extract specific data fields from HTML and generate XPath or CSS selectors"
    
    def _run(self, html_content: str, fields: List[str], mode: str = "xpath",
             language: str = "zh_CN", name: str = "", groups: List[str] = None, **kwargs) -> Dict[str, Any]:
        """提取指定字段"""
        from core.task_prompt import (
            field_extract_prompt_xpath,
            field_extract_prompt_selector,
            specified_field_extract_prompt_xpath,
            specified_field_extract_prompt_selector,
            field_first_prompt_xpath,
            specified_field_first_extract_prompt_xpath
        )
        
        system_kwargs = {"language": language}
        user_kwargs = {"name": name, "html_doc": html_content}
        
        # 选择合适的提示词
        if groups:  # 分组提取
            user_kwargs["fields"] = fields
            user_kwargs["groups"] = groups
            prompt = (
                specified_field_first_extract_prompt_xpath
                if mode == "xpath"
                else specified_field_extract_prompt_selector
            )
        elif fields:  # 指定字段提取
            user_kwargs["fields"] = fields
            prompt = (
                specified_field_extract_prompt_xpath
                if mode == "xpath"
                else specified_field_extract_prompt_selector
            )
        else:  # 通用字段提取
            prompt = (
                field_first_prompt_xpath
                if mode == "xpath"
                else field_extract_prompt_selector
            )
        
        messages = message_formatter(prompt, system_kwargs, user_kwargs)
        result = request_llm(
            messages,
            model=CHAT_MODEL,
            verbose=True,
            parse_json=True,
            max_completion_tokens=8192,
        )
        
        return result["content"]


class PaginationAnalysisTool(HTMLAnalysisTool):
    """分页控件分析工具"""
    name: str = "Pagination Analysis Tool"
    description: str = "Analyze pagination controls and identify different types of pagination mechanisms"
    
    def _run(self, html_content: str, mode: str = "xpath", language: str = "zh_CN", **kwargs) -> Dict[str, Any]:
        """分析分页控件"""
        from core.task_prompt import pagination_prompt_xpath, pagination_prompt_selector
        
        system_kwargs = {"language": language}
        user_kwargs = {"html_doc": html_content}
        
        prompt = (
            pagination_prompt_xpath
            if mode == "xpath"
            else pagination_prompt_selector
        )
        
        messages = message_formatter(prompt, system_kwargs, user_kwargs)
        result = request_llm(
            messages,
            model=CHAT_MODEL,
            verbose=True,
            parse_json=True,
        )
        
        return result["content"]


class HTMLOptimizerTool(HTMLAnalysisTool):
    """HTML优化工具"""
    name: str = "HTML Optimizer Tool"
    description: str = "Optimize HTML by removing redundant elements and finding similar element groups"
    
    def _run(self, html_content: str, mode: str = "xpath", language: str = "zh_CN",
             operation: str = "reduce", **kwargs) -> Dict[str, Any]:
        """HTML优化操作"""
        from core.task_prompt import dom_reducer_prompt_xpath
        
        if operation == "reduce":
            return self._reduce_html(html_content, language)
        elif operation == "find_similar":
            return self._find_similar_groups(html_content, language)
        else:
            raise ValueError(f"Unknown operation: {operation}")
    
    def _reduce_html(self, html_content: str, language: str) -> Dict[str, Any]:
        """精简HTML"""
        html = html_content
        similar_xpaths = []
        
        while True:
            truncated_html_docs = truncate_text_tokens(
                html, max_tokens=200000, encoding_name="o200k_base"
            )
            if len(truncated_html_docs) == 1:
                break
            
            system_kwargs = {"language": language}
            user_kwargs = {"html_doc": truncated_html_docs[:1]}
            messages = message_formatter(
                dom_reducer_prompt_xpath, system_kwargs, user_kwargs
            )
            
            result = request_llm(
                messages,
                model=CHAT_MODEL,
                verbose=True,
                parse_json=True,
            )
            
            html = truncate_html_by_xpath(
                html, [item["xpath"] for item in result["content"]]
            )
            similar_xpaths.extend(result["content"])
        
        return {
            "truncate_html": html,
            "similar_group": similar_xpaths
        }
    
    def _find_similar_groups(self, html_content: str, language: str) -> Dict[str, Any]:
        """查找相似元素组"""
        from core.task_prompt import dom_reducer_prompt_xpath
        
        system_kwargs = {"language": language}
        user_kwargs = {"html_doc": html_content}
        messages = message_formatter(
            dom_reducer_prompt_xpath, system_kwargs, user_kwargs
        )
        
        result = request_llm(
            messages,
            model=CHAT_MODEL,
            verbose=True,
            parse_json=True,
        )
        
        return {"similar_group": result["content"]}


class ValidationTool(HTMLAnalysisTool):
    """验证和修正工具"""
    name: str = "Validation Tool"
    description: str = "Validate and refine XPath/CSS selectors to ensure accuracy"
    
    def _run(self, html_content: str, results: List[Dict], mode: str = "xpath",
             language: str = "zh_CN", result_type: str = "fields", **kwargs) -> List[Dict]:
        """验证和修正结果"""
        if result_type == "fields":
            return self._validate_fields(html_content, results, mode, language)
        elif result_type == "areas":
            return self._validate_areas(html_content, results, mode, language)
        else:
            return results
    
    def _validate_fields(self, html_content: str, results: List[Dict], 
                        mode: str, language: str) -> List[Dict]:
        """验证字段结果"""
        error_dict = {}
        
        # 验证每个结果
        for item in results:
            field = item.get("type") or item.get("field")
            path = item.get("xpath") if mode == "xpath" else item.get("selector")
            
            if path:
                if mode == "xpath":
                    if not is_valid_xpath(path, html_content):
                        error_dict[field] = path
                else:
                    if not is_valid_selector(path, html_content):
                        error_dict[field] = path
        
        # 如果有错误，进行修正
        if error_dict:
            corrected = self._refine_fields(html_content, error_dict, mode, language)
            for item in results:
                field = item.get("type") or item.get("field")
                if field in corrected:
                    if mode == "xpath":
                        item["xpath"] = corrected[field]
                    else:
                        item["selector"] = corrected[field]
        
        return results
    
    def _validate_areas(self, html_content: str, results: List[Dict], 
                       mode: str, language: str) -> List[Dict]:
        """验证区域结果"""
        error_dict = {}
        
        for item in results:
            xpath = item.get("xpath") if mode == "xpath" else item.get("container_selector")
            children_path = (
                item.get("children_xpath")
                if mode == "xpath"
                else item.get("children_selector")
            )
            name = item.get("name")
            
            if mode == "xpath":
                if not (is_valid_xpath(xpath, html_content) and 
                       is_valid_xpath(children_path, html_content)):
                    error_dict[name] = {
                        "description": item.get("description"),
                        "xpath": xpath,
                        "children_xpath": children_path,
                    }
        
        # 如果有错误，进行修正
        if error_dict:
            corrected = self._refine_areas(html_content, error_dict, mode, language)
            for item in results:
                name = item.get("name")
                if name in corrected:
                    if mode == "xpath":
                        item["xpath"] = corrected[name]["xpath"]
                        item["children_xpath"] = corrected[name]["children_xpath"]
        
        return results
    
    def _refine_fields(self, html_content: str, error_fields: Dict, 
                      mode: str, language: str) -> Dict:
        """修正字段错误"""
        from core.task_prompt import reflection_prompt_xpath, reflection_prompt_selector
        
        system_kwargs = {"language": language}
        user_kwargs = {
            "html_doc": html_content,
            "fields": list(error_fields.keys()),
            "result": error_fields,
        }
        
        prompt = reflection_prompt_xpath if mode == "xpath" else reflection_prompt_selector
        messages = message_formatter(prompt, system_kwargs, user_kwargs)
        
        result = request_llm(
            messages,
            model=CHAT_MODEL,
            verbose=True,
            parse_json=True,
            max_completion_tokens=8192,
        )
        
        field_key = "xpath" if mode == "xpath" else "selector"
        return {item["field"]: item[field_key] for item in result["content"]}
    
    def _refine_areas(self, html_content: str, error_fields: Dict, 
                     mode: str, language: str) -> Dict:
        """修正区域错误"""
        from core.task_prompt import reflection_prompt_xpath_area
        
        system_kwargs = {"language": language}
        user_kwargs = {
            "html_doc": html_content,
            "result": error_fields,
        }
        
        messages = message_formatter(reflection_prompt_xpath_area, system_kwargs, user_kwargs)
        
        result = request_llm(
            messages,
            model=CHAT_MODEL,
            verbose=True,
            parse_json=True,
            max_completion_tokens=8192,
        )
        
        return {item["name"]: item for item in result["content"]}


# Agent定义
def create_html_structure_analyst() -> Agent:
    """创建HTML结构分析师"""
    return Agent(
        role="HTML结构分析师",
        goal="分析网页HTML结构，识别和定位各种语义区域",
        backstory="""你是一位资深的网页结构分析专家，拥有多年的前端开发和网页分析经验。
        你擅长快速理解复杂的HTML结构，能够准确识别导航栏、内容区、侧边栏、页脚、
        广告区、翻页控件等各种语义区域。你的分析结果为后续的数据提取工作奠定了基础。""",
        tools=[StructureAnalysisTool()],
        verbose=True,
        allow_delegation=False,
        max_iter=3,
    )


def create_field_extraction_expert() -> Agent:
    """创建数据字段提取专家"""
    return Agent(
        role="数据字段提取专家", 
        goal="根据字段名称精确提取HTML中的数据元素，生成准确的XPath或CSS选择器",
        backstory="""你是一位数据提取领域的专家，精通XPath和CSS选择器语法。
        你能够根据字段的语义含义，在复杂的HTML结构中准确定位目标元素。
        你特别擅长处理电商、新闻、招聘等各类网站的数据提取需求，
        能够生成稳定可靠的元素定位路径。""",
        tools=[FieldExtractionTool()],
        verbose=True,
        allow_delegation=False,
        max_iter=3,
    )


def create_pagination_analyst() -> Agent:
    """创建分页控件分析师"""
    return Agent(
        role="分页控件分析师",
        goal="识别和分析各种类型的分页控件，包括传统分页、加载更多、无限滚动等",
        backstory="""你是分页机制分析的专家，对各种网站的分页实现方式了如指掌。
        你能够识别传统的数字分页、上一页下一页按钮、加载更多按钮、
        无限滚动等各种分页模式，并准确提取相关的控制元素。
        你的分析帮助用户实现完整的多页数据采集。""",
        tools=[PaginationAnalysisTool()],
        verbose=True,
        allow_delegation=False,
        max_iter=2,
    )


def create_html_optimizer() -> Agent:
    """创建HTML优化专家"""
    return Agent(
        role="HTML优化专家",
        goal="优化HTML结构，移除冗余元素，识别相似元素组，提高分析效率",
        backstory="""你是HTML优化和性能调优的专家，擅长分析和简化复杂的网页结构。
        你能够识别并移除对数据提取无用的元素，如广告、脚本、样式等，
        同时保留关键的数据结构。你还能识别页面中的重复元素组，
        为批量数据处理提供支持。""",
        tools=[HTMLOptimizerTool()],
        verbose=True,
        allow_delegation=False,
        max_iter=2,
    )


def create_quality_validator() -> Agent:
    """创建质量验证专家"""
    return Agent(
        role="质量验证专家",
        goal="验证和修正XPath/CSS选择器的准确性，确保数据提取的可靠性",
        backstory="""你是质量保证领域的专家，拥有敏锐的错误检测能力。
        你能够快速验证XPath和CSS选择器的有效性，发现潜在的定位问题，
        并提供准确的修正方案。你的工作确保了数据提取的高成功率和稳定性。
        你特别注重边界情况的处理和异常情况的预防。""",
        tools=[ValidationTool()],
        verbose=True,
        allow_delegation=False,
        max_iter=2,
    )


def create_chat_assistant() -> Agent:
    """创建智能对话助手"""
    return Agent(
        role="智能对话助手",
        goal="协调其他专家的工作，为用户提供智能化的数据采集指导和建议",
        backstory="""你是一位经验丰富的项目协调者和用户顾问，擅长理解用户需求，
        协调各个专业团队的工作。你能够将复杂的技术概念转化为用户易懂的建议，
        为数据采集项目提供全程指导。你熟悉各种网站类型和数据提取场景，
        能够为用户制定最优的采集策略。""",
        tools=[],
        verbose=True,
        allow_delegation=True,
        max_iter=3,
    )
