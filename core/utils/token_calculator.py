"""
Module for truncatinh in chunks the messages
"""

from typing import List

import tiktoken
from lxml import etree


def truncate_text_tokens(text: str, max_tokens: int, encoding_name: str) -> List[str]:
    """
    Truncates text into chunks that are small enough to be processed by specified llm models.

    Args:
        text (str): The input text to be truncated.
        encoding_name (str): The encoding strategy used to encode the text before truncation.

    Returns:
        List[str]: A list of text chunks, each within the token limit of the specified model.

    Example:
        >>> truncate_text_tokens("This is a sample text for truncation.", "GPT-3", "EMBEDDING_ENCODING")
        ["This is a sample text", "for truncation."]

    This function ensures that each chunk of text can be tokenized by the specified model without exceeding the model's token limit.
    """

    encoding = tiktoken.get_encoding(encoding_name)
    encoded_text = encoding.encode(text)
    
    chunks = [
        encoded_text[i : i + max_tokens]
        for i in range(0, len(encoded_text), max_tokens)
    ]
    print(f"encoded tokens:{len(encoded_text)}, chunks:{len(chunks)}")
    result = [encoding.decode(chunk) for chunk in chunks]

    return result

def truncate_html_by_xpath(html, xpath_list, keep_rate=0.5, max_tokens=120000, encoding_name="o200k_base"):
    """
    Truncates HTML by extracting text content using XPath.
    """
    

    encoding = tiktoken.get_encoding(encoding_name)
    encoded_text = encoding.encode(html)
    print(f"before truncate:{len(encoded_text)}")

    # Parse the HTML
    root = etree.HTML(html)

    # Extract text content using XPath, 循环3次
    count = 0
    while count<3:
        if len(encoded_text) < max_tokens:
            break
        for xpath in xpath_list:
            elements = root.xpath(xpath)
            # 保留一定比例的元素或最少3个
            keep_items = max(int(len(elements) * keep_rate), 3) 
            elements_to_retain = set(elements[:keep_items])
            # 遍历所有符合 XPath 的元素，删除不需要保留的元素
            for elem in elements:
                if elem not in elements_to_retain:
                    parent = elem.getparent()
                    if parent is not None:
                        parent.remove(elem)
        reduced_html = etree.tostring(root, pretty_print=False, encoding='unicode')
        encoded_text = encoding.encode(reduced_html)
        count += 1
        print(f"truncate {count} times:{len(encoded_text)}, {keep_items=}")
    print(f"after truncate:{len(encoded_text)}")
    #print(reduced_html)
    return reduced_html[:max_tokens]

if __name__=="__main__":
    with open("html_docs/goodlist.html", "r") as f:
        html_content = f.read()
    result = truncate_html_by_xpath(html_content,["//table[contains(@class, 'list-items')]","//li[contains(@class, 'prices-warp')]","//div[contains(@class, 'params-list')]"])
    