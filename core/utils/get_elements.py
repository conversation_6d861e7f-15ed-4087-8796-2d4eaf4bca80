from bs4 import BeautifulSoup
from lxml import etree


def get_elements(xpath: str, html_doc: str) -> list:
    """根据 xpath 获取 elements"""
    tree = etree.HTML(html_doc)

    elements = []
    for element in tree.xpath(xpath):
        if isinstance(element, str):
            elements.append(element)
        else:
            elements.append(etree.tostring(element, pretty_print=True).decode())
    return elements


def is_valid_xpath(xpath: str, html_doc: str) -> bool:
    """验证 xpath 是否有效,目前只是在于判断是否获取到元素"""
    try:
        elements = get_elements(xpath, html_doc)
        if len(elements) > 0:
            return True
        else:
            return False
    except Exception:
        return False


def is_valid_selector(selector: str, html_doc: str) -> bool:
    """验证 CSS 选择器是否有效"""
    try:
        soup = BeautifulSoup(html_doc, "html.parser")
        elements = soup.select(selector)
        return len(elements) > 0
    except Exception:
        return False
