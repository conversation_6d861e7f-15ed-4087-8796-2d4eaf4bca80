import json
import os
import re
from dataclasses import dataclass
from typing import Dict, List, Optional, Union

import requests
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential  

API_BASE = os.getenv("API_BASE", "https://litellm.nlp.yuntingai.com")
API_KEY = os.getenv("API_KEY", "sk-0hyQ1IdbZ1DM_SEKraTeQQ")
CHAT_MODEL = os.getenv("CHAT_MODEL", "gemini-2.5-flash-preview-04-17")


@dataclass
class Prompt:
    system: str = ""
    user: str = ""
    assistant: str = ""


class LLMState:
    """管理对话上下文信息的状态类"""

    def __init__(self):
        self._messages: List[Dict[str, str]] = []

    def add_message(self, message: Dict[str, str]) -> None:
        self._messages.append(message)

    def get_messages(self, limit: Optional[int] = None) -> List[Dict[str, str]]:
        if limit is None:
            return self._messages
        return self._messages[-limit:]

    def clear(self) -> None:
        self._messages = []

@retry(  
    stop=stop_after_attempt(3),  
    wait=wait_exponential(multiplier=1, min=4, max=10)  
)  
def request_llm(
    messages: List[Dict[str, str]],
    model: str = "gpt-4o-mini",
    stream: bool = False,
    temperature: float = 0.1,
    max_completion_tokens: int = 4096,
    verbose: bool = False,
    parse_json: bool = False,
    metadata: dict = None,
) -> Union[Dict, str]:
    """请求大模型，支持流式和非流式请求"""

    data = {
        "messages": messages,
        "model": model,
        "temperature": temperature,
        "max_completion_tokens": max_completion_tokens,
        "stream": stream,
        "metadata": metadata if metadata else {},
    }
    if stream:
        data.update({"stream_options": {"include_usage": True}})
    if verbose:
        logger.info(f"send to litellm, model:{model}")
    response = requests.post(
        f"{API_BASE}/chat/completions",
        headers={"Authorization": f"Bearer {API_KEY}"},
        json=data,
        stream=stream,
    )
    response.raise_for_status()
    if stream:
        return _handle_stream_response(response, verbose)
    else:
        return _handle_non_stream_response(response.json(), verbose, parse_json)


def _handle_non_stream_response(
    response: Dict, verbose: bool, parse_json: bool = False
) -> Union[Dict, str]:
    """处理非流式响应"""
    if verbose:
        logger.info(f"LLM output: {response}")
    try:
        content = response["choices"][0]["message"]["content"]
        print(content)

        if parse_json:
            try:
                parsed_content = extract_JSON(content)
                return {
                    "content": json.loads(parsed_content),
                    "usage": response["usage"],
                    "created": response["created"],
                }
            except json.JSONDecodeError as e:
                logger.error(f"Parse JSON error: {e}")
                raise ValueError(f"Response is not a valid JSON: {content}") from e
        return {
            "content": content,
            "usage": response["usage"],
            "created": response["created"],
        }

    except KeyError as e:
        logger.error(f"Error parsing LLM response: {e}")
        raise ValueError("Cannot parse LLM response") from e


def _handle_stream_response(response, verbose: bool) -> str:  # type: ignore
    """处理流式响应
    to-do: 支持流式工具调用等json格式的验证和解析
    """
    for line in response.iter_lines():
        if line:
            decoded_line = line.decode("utf-8")
            if verbose:
                logger.info(f"Streamed line: {decoded_line}")

            # # 只处理包含 "data: " 的行
            # if decoded_line.startswith("data: "):
            #     try:
            #         # 移除 "data: " 前缀并解析 JSON
            #         data = json.loads(decoded_line.replace("data: ", ""))
            #         if "choices" in data and len(data["choices"]) > 0:
            #             content = data["choices"][0].get("delta", {}).get("content", "")
            #             if content:
            #                 accumulated_response += content
            #                 if verbose:
            #                     logger.info(
            #                         f"Accumulated response: {accumulated_response}"
            #                     )
            #     except json.JSONDecodeError:
            #         continue

            yield decoded_line


def extract_JSON(text: str) -> str:
    """提取 JSON string
    支持以下格式:
    1. 纯JSON对象或数组
    2. Markdown代码块中的JSON
    3. 文本中包含的JSON对象或数组
    """
    if not text or not isinstance(text, str):
        raise ValueError("文本必须是非空字符串")

    text = text.strip()

    try:
        # 首先尝试解析markdown代码块
        markdown_patterns = [
            r"```(?:json)?\s*([\s\S]*?)```",  # markdown 代码块
            r"`([\s\S]*?)`",  # 行内代码
        ]

        for pattern in markdown_patterns:
            matches = re.findall(pattern, text)
            if matches:
                text = matches[0].strip()
                break

        # 查找JSON对象或数组，search 只返回第一个匹配项
        json_pattern = r"(\{[\s\S]*\}|\[[\s\S]*\])"
        match = re.search(json_pattern, text)

        if match:
            json_str = match.group(1)
            # 验证是否为有效的JSON
            json.loads(json_str)  # 如果无效会抛出异常
            return json_str

        raise ValueError("未找到有效的JSON内容")

    except json.JSONDecodeError as e:
        logger.error(f"JSON parse error: {str(e)}")
        raise ValueError(f"Extracted content is not a valid JSON: {text}")
    except Exception as e:
        logger.error(f"Error extracting JSON: {str(e)}\nText content:\n{text}")
        raise ValueError("JSON extraction failed") from e


def message_formatter(
    prompt: Union[Prompt, str],
    system_kwargs: dict = None,
    user_kwargs: dict = None,
    assistant_kwargs: dict = None,  # 新增 assistant 格式化参数
) -> List[Dict[str, str]]:
    """格式化消息, 支持系统提示、用户提示和助手提示的参数替换

    Args:
        prompt: 包含system、user和assistant模板的提示对象或字符串
        system_kwargs: 系统提示的格式化参数
        user_kwargs: 用户提示的格式化参数
        assistant_kwargs: 助手提示的格式化参数
    """
    messages = []

    # 处理字符串形式的提示词
    if isinstance(prompt, str):
        prompt = Prompt(user=prompt)

    # 添加系统提示
    if prompt.system:
        content = (
            prompt.system.format(**system_kwargs) if system_kwargs else prompt.system
        )
        messages.append(
            {
                "role": "system",
                "content": content,
            }
        )

    # 添加用户提示
    if prompt.user:
        content = prompt.user.format(**user_kwargs) if user_kwargs else prompt.user
        messages.append(
            {
                "role": "user",
                "content": [{"type": "text", "text": content}],
            }
        )

    # 添加助手提示
    if prompt.assistant:
        content = (
            prompt.assistant.format(**assistant_kwargs)
            if assistant_kwargs
            else prompt.assistant
        )
        messages.append(
            {
                "role": "assistant",
                "content": content,
            }
        )

    return messages


if __name__ == "__main__":
    # stream = True
    # if stream:
    #     for line in request_llm(
    #         [{"role": "user", "content": "你好"}],
    #         parse_json=False,
    #         stream=stream,
    #         verbose=True,
    #     ):
    #         data = line.replace("data: ", "")
    #         try:
    #             print(json.loads(data))
    #         except json.JSONDecodeError:
    #             print(data)
    # else:
    #     print(
    #         request_llm(
    #             [{"role": "user", "content": "你好"}],
    #             parse_json=False,
    #             stream=stream,
    #             verbose=True,
    #         )
    #     )

    # 测试JSON extract
    test_cases = [
        '{"name": "张三", "age": 25}',
        # 嵌套
        """{
            "person": {
                "name": "张三",
                "address": {
                    "city": "北京"
                }
            }
        }""",
        """{
            "names": ["张三", "李四"],
            "scores": [{"subject": "数学", "score": 90}]
        }""",
        """```json
[
    {
        "type": "导航栏",
        "xpath": "//div[@class='w']//ul[@class='fl']"
    },
    {
        "type": "搜索框",
        "xpath": "//div[@class='form']"
    },
    {
        "type": "侧边栏",
        "xpath": "//div[@class='m-aside']"
    },
    {
        "type": "广告栏",
        "xpath": "//div[@class='m bottom-ad']"
    },
    {
        "type": "翻页栏",
        "xpath": "//div[@class='f-pager']"
    },
    {
        "type": "商品列表",
        "xpath": "//div[@class='goods-list-v2']"
    }
]
```
""",
        """You have selected the "product list" area for extraction. Please specify the fields you want to extract from this area, such as product name, price, or description.

```json
{
"user_language": "en_US",
"analysis": "User has confirmed the extraction area as 'product list'. Next step is to define the fields to extract.",
"answer": "Please specify the fields you want to extract from the 'product list' area, such as product name, price, or description.",
"next_step": 4
}
```""",
    ]
    for case in test_cases:
        print(extract_JSON(case))
