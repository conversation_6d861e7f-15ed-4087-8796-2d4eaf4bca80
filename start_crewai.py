#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chat4Data CrewAI多Agent系统启动脚本
自动检查依赖、配置环境并启动服务
"""

import os
import sys
import subprocess
import importlib
import time
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 10):
        print("❌ Python版本过低，需要Python 3.10或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'fastapi',
        'uvicorn', 
        'crewai',
        'crewai_tools',
        'requests',
        'pydantic',
        'tiktoken',
        'loguru'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖包"""
    if not packages:
        return True
    
    print(f"\n📦 正在安装缺失的依赖包: {', '.join(packages)}")
    
    try:
        # 尝试使用poetry
        if Path("pyproject.toml").exists():
            print("🔧 检测到pyproject.toml，使用Poetry安装...")
            subprocess.run(["poetry", "install"], check=True)
        else:
            # 使用pip安装
            print("🔧 使用pip安装依赖...")
            for package in packages:
                subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)
        
        print("✅ 依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 未找到pip或poetry，请手动安装依赖")
        return False

def check_environment():
    """检查环境变量配置"""
    required_env = {
        'API_KEY': 'LLM API密钥',
        'API_BASE': 'LLM API基础URL'
    }
    
    missing_env = []
    
    for env_var, description in required_env.items():
        if not os.getenv(env_var):
            missing_env.append((env_var, description))
        else:
            print(f"✅ {env_var}: {os.getenv(env_var)[:20]}...")
    
    if missing_env:
        print("\n⚠️  缺少环境变量配置:")
        for env_var, description in missing_env:
            print(f"   {env_var}: {description}")
        
        print("\n🔧 请设置环境变量:")
        print("export API_KEY='your-api-key'")
        print("export API_BASE='https://litellm.nlp.yuntingai.com'")
        print("export CHAT_MODEL='gemini-2.0-flash-001'")
        
        return False
    
    return True

def check_core_files():
    """检查核心文件是否存在"""
    required_files = [
        'core/agents.py',
        'core/tasks.py', 
        'core/crew.py',
        'core/llm_v2.py',
        'core/task_prompt.py',
        'run_api_crewai.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少核心文件: {missing_files}")
        return False
    
    return True

def start_server(port=18603, workers=2):
    """启动CrewAI服务器"""
    print(f"\n🚀 启动Chat4Data CrewAI多Agent系统...")
    print(f"   端口: {port}")
    print(f"   工作进程: {workers}")
    print(f"   访问地址: http://localhost:{port}")
    print(f"   API文档: http://localhost:{port}/docs")
    
    try:
        # 设置CrewAI环境变量
        os.environ["OPENAI_API_BASE"] = os.getenv("API_BASE", "https://litellm.nlp.yuntingai.com")
        os.environ["OPENAI_API_KEY"] = os.getenv("API_KEY", "")
        os.environ["OPENAI_MODEL_NAME"] = os.getenv("CHAT_MODEL", "gemini-2.0-flash-001")
        
        # 启动服务
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "run_api_crewai:app",
            "--host", "0.0.0.0",
            "--port", str(port),
            "--workers", str(workers)
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_tests():
    """运行测试"""
    print("\n🧪 运行系统测试...")
    
    try:
        subprocess.run([sys.executable, "test_crewai_demo.py"], check=True)
        print("✅ 测试完成")
    except subprocess.CalledProcessError:
        print("❌ 测试失败")
    except FileNotFoundError:
        print("⚠️  测试文件不存在，跳过测试")

def main():
    """主函数"""
    print("🤖 Chat4Data CrewAI多Agent系统启动器")
    print("=" * 50)
    
    # 1. 检查Python版本
    print("\n1️⃣ 检查Python版本...")
    if not check_python_version():
        sys.exit(1)
    
    # 2. 检查核心文件
    print("\n2️⃣ 检查核心文件...")
    if not check_core_files():
        print("请确保所有CrewAI系统文件都已正确创建")
        sys.exit(1)
    
    # 3. 检查依赖
    print("\n3️⃣ 检查依赖包...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        install_choice = input(f"\n是否自动安装缺失的依赖包? (y/n): ").lower()
        if install_choice == 'y':
            if not install_dependencies(missing_packages):
                sys.exit(1)
        else:
            print("请手动安装依赖包后重新运行")
            sys.exit(1)
    
    # 4. 检查环境变量
    print("\n4️⃣ 检查环境配置...")
    if not check_environment():
        sys.exit(1)
    
    # 5. 启动选项
    print("\n5️⃣ 启动选项:")
    print("1. 启动CrewAI多Agent系统")
    print("2. 运行系统测试")
    print("3. 启动系统并运行测试")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        start_server()
    elif choice == "2":
        run_tests()
    elif choice == "3":
        # 在后台启动服务
        print("🚀 启动服务...")
        import threading
        server_thread = threading.Thread(target=lambda: start_server())
        server_thread.daemon = True
        server_thread.start()
        
        # 等待服务启动
        time.sleep(5)
        
        # 运行测试
        run_tests()
    else:
        print("无效选择，启动默认服务...")
        start_server()

if __name__ == "__main__":
    main()
