# 🚀 Chat4Data 小白用户快速入门指南

## 🤔 这个项目是做什么的？

想象一下，你有一个网页，上面有很多商品信息、新闻文章或者其他数据。通常你需要手动复制粘贴这些信息，非常麻烦。

**Chat4Data 就是一个"智能助手"，它可以：**
- 🔍 自动分析网页结构（就像人眼看网页一样）
- 📝 自动找到你想要的数据字段（比如商品名称、价格等）
- 🤖 通过对话告诉你如何提取这些数据
- ⚡ 大大提高数据收集的效率

## 🎯 适用场景

### 场景1：电商数据收集
你想收集淘宝、京东上的商品信息：
- 商品名称
- 价格
- 评价数量
- 商品图片

### 场景2：新闻资讯抓取
你想从新闻网站收集：
- 文章标题
- 发布时间
- 作者
- 正文内容

### 场景3：招聘信息整理
你想从招聘网站收集：
- 职位名称
- 公司名称
- 薪资范围
- 工作地点

## 🛠️ 5分钟快速体验

### 第1步：检查环境
确保你的电脑已安装Python（版本3.10或更高）

```bash
# 检查Python版本
python --version
```

### 第2步：下载项目
```bash
# 下载项目到本地
git clone <项目地址>
cd chat4data_ori
```

### 第3步：安装依赖
```bash
# 安装所需的软件包
pip install -r requirements.txt
```

### 第4步：启动服务
```bash
# 启动智能分析服务
python run_api.py
```

看到这样的信息就说明启动成功了：
```
INFO:     Uvicorn running on http://0.0.0.0:18603
```

### 第5步：打开浏览器测试
在浏览器中访问：`http://localhost:18603/docs`

你会看到一个漂亮的API文档界面，这就是你的智能助手！

## 📱 实际使用示例

### 示例1：分析简单网页

假设你有这样一个商品页面的HTML：

```html
<div class="product">
    <h2 class="title">苹果iPhone 15</h2>
    <span class="price">¥5999</span>
    <button class="buy-btn">立即购买</button>
</div>
```

**使用步骤：**

1. 打开 `http://localhost:18603/docs`
2. 找到 `/analyze_html` 接口
3. 点击 "Try it out"
4. 在 `data` 字段粘贴你的HTML代码
5. 点击 "Execute"

**系统会自动告诉你：**
- 这个页面有哪些主要区域
- 每个区域的定位方法（XPath路径）

### 示例2：提取指定字段

如果你想提取"商品标题"和"价格"：

1. 使用 `/extract_fields` 接口
2. 在 `fields` 中填入：`["商品标题", "价格"]`
3. 系统会自动找到对应的元素位置

**返回结果类似：**
```json
{
  "data": [
    {
      "field": "商品标题",
      "xpath": "//h2[@class='title']",
      "description": "商品的主标题"
    },
    {
      "field": "价格",
      "xpath": "//span[@class='price']",
      "description": "商品的售价"
    }
  ]
}
```

## 🎨 界面操作指南

### API文档界面说明

当你打开 `http://localhost:18603/docs` 后，你会看到：

1. **绿色的POST按钮**：这些是不同的功能
2. **点击展开**：可以看到详细的参数说明
3. **Try it out**：可以直接在网页上测试
4. **Execute**：执行你的请求
5. **Response**：查看返回结果

### 主要功能按钮说明

| 功能 | 用途 | 适合新手 |
|------|------|----------|
| `/analyze_html` | 分析网页整体结构 | ⭐⭐⭐ 推荐先用这个 |
| `/extract_fields` | 提取指定数据字段 | ⭐⭐⭐ 最常用 |
| `/extract_pagination_info` | 分析翻页按钮 | ⭐⭐ 处理多页数据时用 |
| `/reduce_html` | 简化复杂网页 | ⭐ 网页太复杂时用 |
| `/chat_messages` | AI对话助手 | ⭐⭐⭐ 不懂就问它 |

## 💡 新手常见问题

### Q1：我不懂编程，能用这个工具吗？
**答：** 完全可以！你只需要：
- 会复制粘贴HTML代码
- 会在网页界面点击按钮
- 会看懂返回的结果

### Q2：怎么获取网页的HTML代码？
**答：** 
1. 在网页上右键点击
2. 选择"查看页面源代码"或"检查元素"
3. 复制你需要的HTML部分

### Q3：返回的XPath是什么？
**答：** XPath就像是"地址"，告诉程序在网页的哪个位置能找到你要的数据。你可以把它理解为"第3栋楼第2层第5个房间"这样的地址。

### Q4：如果提取结果不准确怎么办？
**答：** 
1. 尝试使用 `/chat_messages` 接口，直接问AI助手
2. 检查HTML代码是否完整
3. 尝试简化HTML（使用 `/reduce_html`）

## 🎯 实用技巧

### 技巧1：从简单开始
- 先用一个简单的HTML测试
- 确保服务正常工作
- 再处理复杂的真实网页

### 技巧2：善用AI对话
当你不知道怎么操作时，使用 `/chat_messages`：
```
"我想从这个网页提取商品信息，应该怎么做？"
```

### 技巧3：分步骤处理
1. 先用 `/analyze_html` 了解页面结构
2. 再用 `/extract_fields` 提取具体字段
3. 最后验证结果是否正确

### 技巧4：保存有用的结果
把系统返回的XPath路径保存下来，下次遇到类似网页可以直接使用。

## 🚀 进阶使用

当你熟悉基本操作后，可以尝试：

1. **批量处理**：一次提取多个字段
2. **多语言支持**：处理英文、日文等网页
3. **复杂页面**：处理有分页、动态加载的网页
4. **自动化脚本**：编写Python脚本自动调用API

## 🆘 遇到问题怎么办？

1. **查看错误信息**：API返回的错误信息通常很有用
2. **检查输入格式**：确保HTML代码格式正确
3. **尝试简化**：如果网页太复杂，先简化再处理
4. **使用对话功能**：直接问AI助手

## 🎉 恭喜你！

如果你能成功运行第一个示例，说明你已经掌握了这个工具的基本使用方法！

现在你可以：
- 分析任何网页的结构
- 自动提取你需要的数据
- 大大提高工作效率

**记住：这个工具就像一个智能助手，不懂就问，多试几次就熟练了！**
