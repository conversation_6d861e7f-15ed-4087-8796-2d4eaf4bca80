import asyncio
import json
import os
import time
import uuid
from typing import Any, Dict, List, Literal, Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from sse_starlette.sse import EventSourceResponse

from core import chat_prompt, chat_prompt_en
from core.llm_v2 import CHAT_MODEL, LLMState, extract_JSON, request_llm

router = APIRouter()


VERSION = os.getenv("VERSION", "EN")
# 定义系统提示词
PROMPT = chat_prompt_en if VERSION == "EN" else chat_prompt
SYSTEM_PROMPT = PROMPT.chat_prompt.system
INTENT_PROMPT = PROMPT.intent_prompt.system


METADATA = {
    #"trace_id": f"chat4data:ai_scraper:{time.strftime('%Y%m%d')}",
    #"trace_user_id": "",
    "trace_name": "chat4data",
    "generation_name": "html_analysis",
    "tags": ["chat"],
}


class Inputs(BaseModel):
    current_site: Optional[str] = None  # 当前网页URL
    candidate_areas: Optional[str] = ""  # 可选择区域
    candidate_fields: Optional[str] = ""  # 可选择字段
    target_site: Optional[str] = None  # 目标采集网页
    target_areas: Optional[str] = None  # 目标区域
    target_fields: Optional[str] = None  # 目标字段
    target_pages: Optional[str] = None  # 目标采集范围


class ChatRequest(BaseModel):
    inputs: Dict = Field(default_factory=dict)  # 预留输入字段
    query: str
    history: List[Dict] = []
    response_mode: Literal["blocking", "streaming"] = "streaming"
    conversation_id: str = ""
    user: str = ""


class Usage(BaseModel):
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0


class RetrieverResource(BaseModel):
    id: str
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ChatCompletionResponse(BaseModel):
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    conversation_id: str
    mode: str = "chat"  # 模式预留
    answer: Dict[str, Any] = Field(default_factory=dict)
    ## 中间结果Dict
    # thinking_results: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(
        default_factory=dict
    )  # 包含 usage 和 retriever_resources或其他字段
    created_at: int = Field(default_factory=lambda: int(time.time()))


class ChunkChatCompletionResponse(BaseModel):
    event: Literal["message", "message_end"]
    task_id: str = ""  # 暂定与openai的返回id对齐，后续用于流式生成撤回
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    conversation_id: str
    answer: Optional[str] = None  # message 事件
    # thinking_results: Dict[str, Any] = Field(default_factory=dict) # 流式解析暂时保留
    metadata: Optional[Dict[str, Any]] = (
        None  # message_end 事件字段，包含 usage 和 retriever_resources
    )
    created_at: int = Field(default_factory=lambda: int(time.time()))


async def process_llm_response(
    conversation_id: str,
    messages: List[dict],
    model: str,
    state: Optional[LLMState] = None,
) -> ChatCompletionResponse:
    """处理LLM响应"""
    try:
        METADATA["trace_id"] = f"chat4data:{time.strftime('%Y%m%d%H')}"
        response = request_llm(
            messages=messages,
            model=model,
            stream=False,
            verbose=False,
            temperature=0.5,
            parse_json=True,
            metadata=METADATA,
        )
        # 从content中获取answer并移除
        response["content"].pop("analysis", "")
        answer = response["content"]
        # answer = response["content"].pop("answer", "")
        # content中剩余的内容作为thinking_results
        # thinking_results = response["content"]

        return ChatCompletionResponse(
            conversation_id=conversation_id,
            answer=answer,
            # thinking_results=thinking_results,
            metadata={"usage": response["usage"]},
            created_at=response["created"],
        ).model_dump()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM请求失败: {str(e)}")


async def stream_llm_response(
    conversation_id: str,
    messages: List[dict],
    model: str,
    state: Optional[LLMState] = None,
) -> ChunkChatCompletionResponse:  # type: ignore
    """流式处理LLM响应"""
    try:
        accumulated_content = ""
        METADATA["trace_id"] = f"chat4data:{time.strftime('%Y%m%d%H')}"
        for chunk in request_llm(
            messages=messages,
            model=model,
            stream=True,
            verbose=False,
            temperature=0.5,
            metadata=METADATA,
        ):
            # 处理SSE数据流
            if isinstance(chunk, str):
                if not chunk.startswith("data: "):
                    continue

                # 移除 "data: " 前缀并解析 JSON
                try:
                    chunk = json.loads(chunk.replace("data: ", ""))
                except json.JSONDecodeError:
                    continue

                if "choices" in chunk and len(chunk["choices"]) > 0:
                    choice = chunk["choices"][0]

                    if "usage" in chunk:
                        # 最后一条数据块
                        accumulated_content = extract_JSON(accumulated_content)
                        response_chunk = ChunkChatCompletionResponse(
                            event="message_end",
                            task_id=chunk["id"],
                            conversation_id=conversation_id,
                            answer=accumulated_content,
                            metadata={"usage": Usage(**chunk["usage"])},
                            created_at=chunk["created"],
                        ).model_dump_json()
                        yield response_chunk
                    else:
                        # 常规消息
                        content = choice.get("delta", {}).get("content", "")
                        if content:
                            accumulated_content += content
                            response_chunk = ChunkChatCompletionResponse(
                                task_id=chunk["id"],
                                event="message",
                                conversation_id=conversation_id,
                                answer=content,
                                metadata=None,
                                created_at=chunk["created"],
                            ).model_dump_json()
                            yield response_chunk
            await asyncio.sleep(0.001)
        # 更新状态
        if state and accumulated_content:
            state.add_message({"role": "assistant", "content": accumulated_content})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM流式请求失败: {str(e)}")


# to-do: 多业务场景，通过id或者鉴权token区分 不同的system_prompt
@router.post("/analyze_intent")
async def analyze_intent(request: ChatRequest):
    state = LLMState()

    # 构建系统消息
    state.add_message(
        {
            "role": "system",
            "content": INTENT_PROMPT,  # .format(inputs=request.inputs),
        }  # inputs字段待定
    )

    # 添加消息历史
    for msg in request.history:
        state.add_message(
            {
                "role": "user" if msg.get("role") == "user" else "assistant",
                "content": msg.get("content", ""),
            }
        )
    state.add_message(
        {
            "role": "assistant",
            "content": f"current state:\n{request.inputs}"
            if VERSION == "EN"
            else f"当前状态信息:\n{request.inputs}",
        }
    )

    # 添加当前用户消息
    format_content = (
        "\n\n** YOUR RESPOND MUST BE IN JSON FORMAT **"
        if VERSION == "EN"
        else "\n\n** 你的回答必须是JSON格式 **"
    )
    current_message = {"role": "user", "content": request.query + format_content}
    state.add_message(current_message)

    # 流式或非流式响应
    if request.response_mode == "streaming":
        return EventSourceResponse(
            stream_llm_response(
                request.conversation_id, state.get_messages(), CHAT_MODEL, state
            ),
        )
    else:
        response = await process_llm_response(
            request.conversation_id, state.get_messages(), CHAT_MODEL, state
        )
        return response


# to-do: 多业务场景，通过id或者鉴权token区分 不同的system_prompt
@router.post("/chat_messages")
async def chat_messages(request: ChatRequest):
    state = LLMState()

    # 构建系统消息
    state.add_message(
        {
            "role": "system",
            "content": SYSTEM_PROMPT,  # .format(inputs=request.inputs),
        }  # inputs字段待定
    )

    # 添加消息历史
    for msg in request.history:
        state.add_message(
            {
                "role": "user" if msg.get("role") == "user" else "assistant",
                "content": msg.get("content", ""),
            }
        )
    state.add_message(
        {
            "role": "assistant",
            "content": f"current state:\n{request.inputs}"
            if VERSION == "EN"
            else f"当前状态信息:\n{request.inputs}",
        }
    )

    # 添加当前用户消息
    format_content = (
        "\n\n** YOUR RESPOND MUST BE IN JSON FORMAT **"
        if VERSION == "EN"
        else "\n\n** 你的回答必须是JSON格式 **"
    )
    current_message = {
        "role": "user",
        "content": f"<user_input>{request.query}</user_input>" + format_content,
    }
    state.add_message(current_message)

    # 流式或非流式响应
    if request.response_mode == "streaming":
        return EventSourceResponse(
            stream_llm_response(
                request.conversation_id, state.get_messages(), CHAT_MODEL, state
            ),
        )
    else:
        response = await process_llm_response(
            request.conversation_id, state.get_messages(), CHAT_MODEL, state
        )
        return response
